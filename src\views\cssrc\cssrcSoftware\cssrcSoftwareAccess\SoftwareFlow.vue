<!-- 添加或修改软件基本信息对话框 -->
<template>
<div>
    <div class="p-2">
        <el-card shadow="never" style="border: 0px;">
            <div style="display: flex; justify-content: space-between">
                <div>
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button style="float: right" @click="goBack()">取 消</el-button>
                </div>
            </div>
        </el-card>
        <el-card shadow="never" style="height: 82vh; border: 0px; overflow-y: auto">
            <el-form ref="cssrcSoftwareAccessFormRef" :disabled="routeParams.type === 'view'" v-loading="loading" :model="form" :rules="dynamicRules" label-width="140px">
                <el-divider content-position="left" style="margin-top:0px;">申请信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="10">
                        <el-form-item label="入网类型" prop="accessType">
                            <el-select v-model="form.accessType" placeholder="请选择" clearable>
                                <el-option value="通用软件库" label="通用软件库" />
                                <el-option value="白名单" label="白名单" />
                                <el-option value="本机" label="本机" />
                                <el-option value="服务器" label="服务器" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="入网申请" prop="accessKind">
                            <el-select v-model="form.accessKind" placeholder="请选择" clearable>
                                <el-option value="新增软件" label="新增软件" />
                                <el-option value="版本更新" label="版本更新" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="10">
                        <el-form-item label="计算机密级编号" prop="computerCode">
                            <el-input v-model="form.computerCode" placeholder="请输入计算机密级编号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="计算机联网类型" prop="networkType">
                            <el-select v-model="form.networkType" placeholder="请选择联网类型" clearable>
                                <el-option value="涉密内网" label="涉密内网" />
                                <el-option value="单机" label="单机" />
                                <el-option value="试验测控网" label="试验测控网" />
                                <el-option value="其他" label="其他" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="10">
                        <el-form-item label="责任人" prop="accessResponsibleNickName">
                            <el-input v-model="form.accessResponsibleNickName" style="width:258px; padding-right:10px;" readonly/>
                            <el-button type="primary" plain icon="Plus" @click="openUserSelect">选择责任人</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-divider content-position="left">软件信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="10" v-if="form.accessType !== '本机' && form.accessType !== '服务器'">
                        <el-form-item label="软件分类" prop="categoryId">
                            <el-tree-select
                                v-model="form.categoryId"
                                :data="filteredCategoryOptions"
                                :props="{ value: 'id', label: 'label', children: 'children' } as any"
                                value-key="id"
                                placeholder="请选择软件分类"
                                check-strictly
                                default-expand-all
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="软件名称" prop="softwareName">
                            <el-input v-if="form.accessKind !== '版本更新'" v-model="form.softwareName" placeholder="请输入软件名称" />
                            <div v-else style="display: flex; width: 100%;">
                                <el-input v-model="form.softwareName" placeholder="请输入软件名称" readonly style="flex: 1;"/>
                                <el-button type="primary" plain icon="Plus" @click="openSoftwareSelect" style="margin-left: 10px;">选择软件</el-button>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">                   
                    <el-col :span="10">
                        <el-form-item label="生产厂商" prop="manufacturer">
                            <el-input v-model="form.manufacturer" placeholder="请输入生产厂商" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="生产国别" prop="country">
                            <el-input v-model="form.country" placeholder="请输入生产国别" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="10">
                        <el-form-item label="密级" prop="secret">
                        <el-select v-model="form.secret" placeholder="请选择密级" >
                            <el-option
                            v-for="dict in sys_file_secret"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                            ></el-option>
                        </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="20">
                        <el-form-item label="软件简介" prop="intro">
                            <el-input v-model="form.intro" type="textarea" placeholder="请输入软件简介" :rows="3" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-divider content-position="left">软件版本信息</el-divider>
                <el-row :gutter="20" >
                    <el-col :span="10">
                        <el-form-item label="版本号" prop="versionName">
                            <el-input v-model="form.versionName" placeholder="请填写软件版本号，如V1.0.0" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="适用平台" prop="platform">
                            <el-select v-model="form.platform" placeholder="请选择适用平台" clearable>
                                <el-option value="windows" label="windows" />
                                <el-option value="linux" label="linux" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="10">
                        <el-form-item label="软件架构" prop="architecture">
                            <el-select v-model="form.architecture" placeholder="请选择软件架构" clearable>
                                <el-option value="x86" label="x86" />
                                <el-option value="x64" label="x64" />
                                <el-option value="arm64" label="arm64" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="包类型" prop="packageType">
                            <el-select v-model="form.packageType" placeholder="请选择包类型" clearable>
                                <el-option value="exe" label="exe" />
                                <el-option value="msi" label="msi" />
                                <el-option value="deb" label="deb" />
                                <el-option value="rpm" label="rpm" />
                                <el-option value="tar.gz" label="tar.gz" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="10">
                        <el-form-item label="系统位数" prop="bits">
                            <el-select v-model="form.bits" placeholder="请选择系统位数" clearable>
                                <el-option value="32" label="32" />
                                <el-option value="64" label="64" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="附件大小(GB)" prop="fileSize">
                            <el-input v-model="form.fileSize" placeholder="请填写附件大小(GB)" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <UserSelect ref="userSelectRef" :multiple="false" :data="selectUserId" @confirm-call-back="userSelectCallBack"></UserSelect>
            <SoftwareSelect ref="softwareSelectRef" :multiple="false" @confirm-call-back="softwareSelectCallBack"></SoftwareSelect>
        </el-card>
    </div>
</div>
</template>
<script setup name="Notice" lang="ts">
import { listCssrcSoftwareAccess, getCssrcSoftwareAccess, delCssrcSoftwareAccess, addCssrcSoftwareAccess, updateCssrcSoftwareAccess } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareAccess';
import { CssrcSoftwareAccessVO, CssrcSoftwareAccessQuery, CssrcSoftwareAccessForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareAccess/types';
import { CssrcSoftwareCategoryVO, CssrcSoftwareCategoryTreeVO } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/types';
import { categoryTreeSelect } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/index';
import { addCssrcSoftwareInfo, updateCssrcSoftwareInfo, listCssrcSoftwareInfo } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo'; // 添加导入
import { CssrcSoftwareInfoForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo/types';
import { startWorkFlow } from '@/api/workflow/task';
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import { AxiosResponse } from 'axios';
import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
import { UserVO } from '@/api/system/user/types';
import UserSelect from '@/components/UserSelect/index.vue';
import { useRouter, useRoute } from 'vue-router';
import SoftwareSelect from '../components/SoftwareSelect.vue';

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_file_secret, wf_business_status } = toRefs<any>(proxy?.useDict('sys_file_secret', 'wf_business_status'));
// 过滤 sys_file_secret 字典数据
const filteredSysFileSecret = computed(() => {
    return sys_file_secret.value.filter((dict: any) => dict.value === '0' || dict.value === '5' || dict.value === '10');
});
const categoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);
const enabledCategoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);

/* 审批组件相关定义 */
const buttonLoading = ref(false);
const loading = ref(true);
const routeParams = ref<Record<string, any>>({}); // 路由参数
const flowCode = ref<string>('');
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>(); // 提交组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>(); // 审批记录组件
const cssrcSoftwareAccessFormRef = ref<ElFormInstance>();
const taskVariables = ref<Record<string, any>>({});
// 定义申请人选择组件
const userSelectRef = ref<InstanceType<typeof UserSelect>>();
// 责任人id
const selectUserId = ref<number | string>();

const initFormData: CssrcSoftwareAccessForm = {
    accessId: undefined,
    accessKind: undefined,
    accessType: undefined,
    accessResponsible: undefined,
    accessResponsibleNickName: undefined,
    networkType: undefined,
    computerCode: undefined,
    flowStatus: '',
    createDept: undefined,
    createBy: undefined,
    createTime: undefined,
    /** 软件信息 */
    softwareId: undefined,
    categoryId: undefined, // 软件分类ID
    categoryName: undefined,
    softwareName: undefined,
    manufacturer: undefined,
    country: undefined,
    intro: undefined,
    secret: undefined,
    /** 软件版本信息 */
    versionName: undefined,
    platform: undefined,
    architecture: undefined,
    packageType: undefined,
    bits: undefined,
    fileSize: undefined
}
const data = reactive<PageData<CssrcSoftwareAccessForm, CssrcSoftwareAccessQuery>>({
    form: {...initFormData},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        accessId: undefined,
        accessKind: undefined,
        accessType: undefined,
        accessResponsible: undefined,
        accessResponsibleNickName: undefined,
        networkType: undefined,
        computerCode: undefined,
        flowStatus: '',
        createDept: undefined,
        createBy: undefined,
        createTime: undefined,
        /** 软件信息 */
        softwareId: undefined,
        categoryId: undefined, // 软件分类ID
        categoryName: undefined,
        softwareName: undefined,
        manufacturer: undefined,
        country: undefined,
        intro: undefined,
        secret: undefined,
        /** 软件版本信息 */
        versionName: undefined,
        platform: undefined,
        architecture: undefined,
        packageType: undefined,
        bits: undefined,
        fileSize: undefined,
        params: {
        }
    },
    rules: {
        accessType: [
            { required: true, message: "入网类型不能为空", trigger: "blur" }
        ],
        accessResponsible: [
            { required: true, message: "责任人不能为空", trigger: "blur" }
        ],
        networkType: [
            { required: true, message: "计算机联网类型不能为空", trigger: "blur" }
        ],
        softwareName: [
            { required: true, message: "软件名称不能为空", trigger: "blur" }
        ],
        secret: [
            { required: true, message: "软件密级不能为空", trigger: "blur" }
        ],
        intro: [
            { required: true, message: "软件简介不能为空", trigger: "blur" }
        ],
        versionName: [
            { required: true, message: "软件版本号不能为空", trigger: "blur" }
        ],
        platform: [
            { required: true, message: "使用平台不能为空", trigger: "blur" }
        ],
        architecture: [
            { required: true, message: "软件架构不能为空", trigger: "blur" }
        ],
        packageType: [
            { required: true, message: "包类型不能为空", trigger: "blur" }
        ],
        bits: [
            { required: true, message: "系统位数不能为空", trigger: "blur" }
        ],
        fileSize: [
            { required: true, message: "附件大小不能为空", trigger: "blur" }
        ],
    }
});
// 动态设置软件分类、生产厂商和生产国别的验证规则
const dynamicRules = computed(() => {
    const rules = { ...data.rules };
    
    // 只有当入网类型不是"本机"和"服务器"时，才需要验证这些字段
    if (form.value.accessType !== '本机' && form.value.accessType !== '服务器') {
        rules.categoryId = [
            { required: true, message: "软件分类不能为空", trigger: "blur" }
        ];
    }
    else {
        // 如果是"本机"或"服务器"，则不需要这些字段的验证规则
        delete rules.categoryId;
        delete rules.manufacturer;
        delete rules.country;
    }
    return rules;
});

const { form, rules } = toRefs(data);

/** 打开责任人选择弹窗 */
const openUserSelect = () => {
    userSelectRef.value.open({
        deptParam: 'unit' // company,department,unit
    });
};
// 责任人选择回调
const userSelectCallBack = (data: UserVO[]) => {
    if (data && data.length === 1) {
        const selectedUser = data[0];
        selectUserId.value = selectedUser.userId;
        form.value.accessResponsibleNickName = selectedUser.nickName;
    }
};

// 定义软件选择组件引用
const softwareSelectRef = ref<InstanceType<typeof SoftwareSelect>>()
// 打开软件选择弹窗
const openSoftwareSelect = () => {
    softwareSelectRef.value?.open()
}
// 软件选择回调
const softwareSelectCallBack = (data: CssrcSoftwareInfoForm[]) => {
    if (data && data.length === 1) {
        const selectedSoftware = data[0];
        // 自动填充软件相关信息
        form.value.softwareName = selectedSoftware.softwareName;
        form.value.categoryId = selectedSoftware.categoryId;
        form.value.categoryName = selectedSoftware.categoryName;
        form.value.manufacturer = selectedSoftware.manufacturer;
        form.value.country = selectedSoftware.country;
        form.value.intro = selectedSoftware.intro;
        form.value.secret = selectedSoftware.secret;
        form.value.softwareId = selectedSoftware.softwareId;
    }
}

// 修改watch监听，当accessKind变化时重置软件相关信息
watch(() => form.value.accessKind, (newVal, oldVal) => {
    if (newVal !== oldVal) {
        if (newVal === '新增软件') {
            // 清空软件相关信息
            form.value.softwareId = undefined
            if (oldVal === '版本更新') {
                form.value.categoryId = undefined
                form.value.categoryName = undefined
                form.value.manufacturer = undefined
                form.value.country = undefined
                form.value.intro = undefined
                form.value.secret = undefined
            }
        }
    }
})

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData };
    cssrcSoftwareAccessFormRef.value?.resetFields();
};

/** 查询软件分类下拉树结构 */
const getCategoryTree = async () => {
const res = await categoryTreeSelect();
  categoryOptions.value = res.data; // 树结构
  enabledCategoryOptions.value = filterDisabledCategory(res.data); // 也是树结构
};

/** 过滤禁用的软件分类 */
const filterDisabledCategory = (categoryList: CssrcSoftwareCategoryTreeVO[]): CssrcSoftwareCategoryTreeVO[] => {
    return categoryList.filter((category) => {
        if (category.disabled) {
        return false;
        }
        if (category.children && category.children.length) {
        category.children = filterDisabledCategory(category.children);
        }
        return true;
    });
};

// 添加计算属性用于过滤软件分类选项
const filteredCategoryOptions = computed(() => {
    if (!form.value.accessType) {
        return enabledCategoryOptions.value;
    }
    return filterCategoryByAccessType(enabledCategoryOptions.value, form.value.accessType);
});
// 根据入网类型过滤分类树
const filterCategoryByAccessType = (categories: CssrcSoftwareCategoryTreeVO[], accessType: string): CssrcSoftwareCategoryTreeVO[] => {
    // 未选择入网类型时返回所有的软件分类树
    if (!accessType) return categories;
    // 确定目标分类ID
    let targetCategoryId: number | undefined;
    if (accessType === '白名单') {
        targetCategoryId = 101;
    } else if (accessType === '通用软件库') {
        targetCategoryId = 103;
    }

    // 如果没有对应的目标分类，则返回所有启用的分类
    if (!targetCategoryId) {
        return categories;
    }

    // 查找目标分类及其所有子分类
    const findTargetCategory = (categoryList: CssrcSoftwareCategoryTreeVO[]): CssrcSoftwareCategoryTreeVO | null => {
        for (const category of categoryList) {
            if (category.id === targetCategoryId) {
                return category;
            }
            // 如果当前分类有子分类，则递归在子分类中查找目标分类，找到目标分类就返回
            if (category.children && category.children.length) {
                const found = findTargetCategory(category.children);
                if (found) {
                    return found;
                }
            }
        }
        // 遍历所有分类都未找到目标分类，返回空值
        return null;
    };
    
    const targetCategory = findTargetCategory(categories);
    return targetCategory ? [targetCategory] : [];
};

// 监听入网类型变化，清空软件分类选择
watch(() => form.value.accessType, (newVal, oldVal) => {
    if (newVal !== oldVal) {
        // 清空软件分类选择
        form.value.categoryId = undefined;
    }
});

/** 获取详情 */
const getInfo = () => {
    loading.value = true;
    buttonLoading.value = false;
    nextTick(async () => {
        const res = await getCssrcSoftwareAccess(routeParams.value.id);
        Object.assign(form.value, res.data);
        loading.value = false;
        buttonLoading.value = false;
    });
};

/**
 * 将审批通过的数据同步到软件信息表
 */
const syncToSoftwareInfo = async (accessData: CssrcSoftwareAccessForm): Promise<void> => {
    try {
        // 构造软件信息表数据
        const softwareInfo: CssrcSoftwareInfoForm = {
            // 基本信息
            categoryId: accessData.categoryId,
            softwareName: accessData.softwareName,
            categoryName: accessData.categoryName,
            manufacturer: accessData.manufacturer,
            country: accessData.country,
            intro: accessData.intro,
            secret: accessData.secret,
            createByNickName: accessData.createBy,
            status: '0', // 默认正常状态
            downloadCount: 0, // 默认下载次数为0
        };

        let softwareId: string | number;

        // 如果是更新操作，需要包含 softwareId
        if (accessData.softwareId) {
            softwareInfo.softwareId = accessData.softwareId;
            await updateCssrcSoftwareInfo(softwareInfo);
            proxy?.$modal.msgSuccess("软件信息更新成功");
        } else {
            // 新增操作
            const res = await addCssrcSoftwareInfo(softwareInfo);
            // 从返回结果中获取新生成的 softwareId
            softwareId = res.data.softwareId;
            proxy?.$modal.msgSuccess("软件信息新增成功");
        }
        // 更新 cssrcSoftwareAccess 表中的 softwareId 字段
        if (accessData.accessId && softwareId) {
            // 创建更新表单，只更新 softwareId 字段
            const updateForm: Partial<CssrcSoftwareAccessForm> = {
                accessId: accessData.accessId,
                softwareId: softwareId
            };
            
            // 更新 access 表中的 softwareId
            await updateCssrcSoftwareAccess(updateForm as CssrcSoftwareAccessForm);
        }
        
        // 更新当前表单中的 softwareId
        form.value.softwareId = softwareId;
    } catch (error) {
        console.error('同步软件信息失败:', error);
        throw new Error('同步软件信息失败');
    }
};

/** 提交按钮 */
const submitForm = () => {
    cssrcSoftwareAccessFormRef.value?.validate(async (valid: boolean) => {
        form.value.accessResponsible = selectUserId.value;
        if (!valid) return; 
        buttonLoading.value = true;
        try{
            if (form.value.accessId) {
                await updateCssrcSoftwareAccess(form.value).finally(() =>  buttonLoading.value = false);
            } else {
                await addCssrcSoftwareAccess(form.value).finally(() =>  buttonLoading.value = false);
            }
            proxy?.$modal.msgSuccess("保存成功");
            // 保存成功后立即调用同步方法
            await handleProcessApproved();

        } catch (error) {
            ElMessage.error('保存失败，请联系管理员');
            console.error(error);
        } finally {
            buttonLoading.value = false;
        }
    });
}
/**
 * 处理流程审批通过后的同步操作
 * 这个方法应该在流程审批通过的回调中调用
 */
const handleProcessApproved = async () => {
    try {
        buttonLoading.value = true;
        // 同步数据到软件信息表
        await syncToSoftwareInfo(form.value);
        proxy?.$modal.msgSuccess("数据同步成功");
    } catch (error) {
        proxy?.$modal.msgError("数据同步失败: " + (error instanceof Error ? error.message : '未知错误'));
        console.error('数据同步失败:', error);
    } finally {
        buttonLoading.value = false;
    }
};
/** 返回按钮 */
const goBack = () => {
    proxy?.$tab.closePage(route);
    router.go(-1);
};

onMounted(() => {
    getCategoryTree();
    nextTick(async () => {
        routeParams.value = route.query;
        reset();
        loading.value = false;
        if (routeParams.value.type === 'update' || routeParams.value.type === 'view') {
            getInfo();
        }
    });
})

</script>
<style scoped lang="scss"></style>
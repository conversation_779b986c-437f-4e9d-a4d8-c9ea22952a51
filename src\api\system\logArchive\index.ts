import request from '@/utils/request';
import { LogArchiveVO, LogArchiveForm, LogArchiveQuery } from '@/api/system/logArchive/types';
import { AxiosPromise } from 'axios';

/**
 * 查询日志转存配置列表
 * @param query
 * @returns {*}
 */
export function listLogArchive(query: LogArchiveQuery): AxiosPromise<LogArchiveVO[]> {
    return request({
        url: '/system/logArchive/config/list',
        method: 'get',
        params: query,
    });
}

/**
 * 修改日志转存配置
 * @param data
 */
export const updateLogArchive = (data: LogArchiveForm) => {
    return request({
        url: '/system/logArchive/config',
        method: 'put',
        data: data,
    });
}

/**
 * 手动执行日志转存
 */
export const executeLogArchive = () => {
    return request({
        url: '/system/logArchive/execute',
        method: 'post'
    });
}
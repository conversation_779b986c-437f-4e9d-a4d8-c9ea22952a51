<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="课题编号" prop="subjectNumber">
              <el-input v-model="queryParams.subjectNumber" placeholder="请输入课题编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="课题名称" prop="subjectName">
              <el-input v-model="queryParams.subjectName" placeholder="请输入课题名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="部门" prop="deptName">
              <el-input v-model="queryParams.deptName" placeholder="请输入部门名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请人" prop="nickName">
              <el-input v-model="queryParams.nickName" placeholder="请输入申请人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="付款时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangePaymentTime"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1), new Date(2000, 1, 1)]"
              />
            </el-form-item>
            <el-form-item prop="isNobalanceProject">
              <label class="longlabel">是否零余额项目</label>
              <el-select style="width: 198px;" v-model="queryParams.isNobalanceProject" placeholder="请选择是或否" clearable @keyup.enter="handleQuery">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="流程状态" prop="flowStatus">
              <el-select v-model="queryParams.flowStatus" placeholder="业务状态" clearable>
                <el-option v-for="dict in wf_business_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['cssrcFinance:expenditurePlan:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['cssrcFinance:expenditurePlan:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="expenditurePlanList" @selection-change="handleSelectionChange" @sort-change="handleSortChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="false" />
        <el-table-column label="申请人" align="center" prop="nickName" :sortable="'custom'" :sort-orders="['ascending', 'descending']"/>
        <el-table-column label="部门" align="center" prop="deptName" />
        <el-table-column label="支出类别" align="center" prop="expenditureType" />
        <!-- <el-table-column label="内容" align="center" prop="content" /> -->
        <el-table-column label="课题编号" align="center" prop="subjectNumber" :sortable="'custom'" :sort-orders="['ascending', 'descending']"/>
        <el-table-column label="课题名称" align="center" prop="subjectName" />
        <el-table-column label="项目类别" align="center" prop="projectType" />
        <el-table-column label="合同总金额" align="center" prop="contractTotalAmount" />
        <el-table-column label="本次付款金额" align="center" prop="currentPaymentAmount" />
        <el-table-column label="付款时间" align="center" prop="paymentTime" width="120" 
          :sortable="'custom'" :sort-orders="['ascending', 'descending']">
          <template #default="scope">
            <span>{{ parseTime(scope.row.paymentTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否零余额项目" align="center" prop="isNobalanceProject" width="120">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isNobalanceProject" />
          </template>
        </el-table-column>
        <el-table-column label="是否重点项目" align="center" prop="isMajorProject">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isMajorProject" />
          </template>
        </el-table-column>
        <el-table-column label="项目是否到款" align="center" prop="isFunded">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isFunded" />
          </template>
        </el-table-column>
        <el-table-column label="支付方式" align="center" prop="paymentMethod" />
        <el-table-column align="center" label="流程状态" prop="flowStatus" width="100">
          <template #default="scope">
            <dict-tag :options="wf_business_status" :value="scope.row.flowStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
          <template #default="scope">
            <el-tooltip content="修改" placement="top"
            v-if="scope.row.flowStatus === 'draft' || scope.row.flowStatus === 'cancel' || scope.row.flowStatus === 'back'">
              <el-button v-hasPermi="['cssrcFinance:expenditurePlan:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top"
            v-if="scope.row.flowStatus === 'draft' || scope.row.flowStatus === 'cancel' || scope.row.flowStatus === 'back'">
              <el-button v-hasPermi="['cssrcFinance:expenditurePlan:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="查看" placement="top">
              <el-button type="primary" link icon="View" @click="handleView(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="撤销" placement="top" 
            v-if="scope.row.flowStatus === 'waiting'">
              <el-button type="primary" link icon="Notification" @click="handleCancelProcessApply(scope.row.id)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="ExpenditurePlan" lang="ts">
import { listExpenditurePlan, delExpenditurePlan } from '@/api/cssrcFinance/budget/expenditurePlan';
import { ExpenditurePlanVO, ExpenditurePlanQuery, ExpenditurePlanForm } from '@/api/cssrcFinance/budget/expenditurePlan/types';
import useAutoSearch from '@/hooks/useAutoSearch'; // 调用自动搜索hook
import { cancelProcessApply } from '@/api/workflow/instance';
import { ref, onMounted, onBeforeUnmount } from 'vue';

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));
const { wf_business_status } = toRefs<any>(proxy?.useDict('wf_business_status'));
const expenditurePlanList = ref<ExpenditurePlanVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangePaymentTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();

const initFormData: ExpenditurePlanForm = {
  id: undefined,
  deptId: '',
  deptName: '',
  userId: '',
  userName: '',
  nickName: '',
  expenditureType: '',
  content: '',
  subjectNumber: '',
  subjectName: '',
  projectType: '',
  contractTotalAmount: undefined,
  currentPaymentAmount: undefined,
  paymentTime: '',
  isNobalanceProject: '',
  isMajorProject: '',
  isFunded: '',
  paymentMethod: '',
  flowStatus: ''
}
const data = reactive<PageData<ExpenditurePlanForm, ExpenditurePlanQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptName: '',
    userId: '',
    userName: '',
    nickName: '',
    expenditureType: '',
    subjectNumber: '',
    subjectName: '',
    projectType: '',
    isNobalanceProject: '',
    params: {
      paymentTime: '',
    },
    flowStatus: '',
    orderColumn: '',
    orderType: ''
  },
  rules: {
    expenditureType: [
      { required: true, message: "支出类别不能为空", trigger: "change" }
    ],
    subjectNumber: [
      { required: true, message: "课题编号不能为空", trigger: "blur" }
    ],
    subjectName: [
      { required: true, message: "课题名称不能为空", trigger: "blur" }
    ],
    projectType: [
      { required: true, message: "项目类别不能为空", trigger: "change" }
    ],
    contractTotalAmount: [
      { required: true, message: "合同总金额不能为空", trigger: "blur" },
      { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: "金额格式错误(小数点后最多两位)", trigger: "blur" }
    ],
    currentPaymentAmount: [
      { required: true, message: "本次付款金额不能为空", trigger: "blur" },
      { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: "金额格式错误(小数点后最多两位)", trigger: "blur" }
    ]
  }
});
// // 自动获取当前用户和部门
// interface State {
//   user: Partial<UserVO>;
// }
// const state = ref<State>({
//   user: {},
// });
// const userForm = ref({});
// const getUser = async () => {
//   const res = await getUserProfile();
//   state.value.user = res.data.user;
//   userForm.value = { ...res.data.user };
// };
const { queryParams } = toRefs(data);

/** 查询财务-预算-支出计划列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangePaymentTime.value, 'PaymentTime');
  const res = await listExpenditurePlan(queryParams.value);
  // 添加对<dict-tag>组件value属性的类型校验，确保所有值都不为空
  expenditurePlanList.value = res.rows.map(item => ({
    ...item,
    isNobalanceProject: item.isNobalanceProject || '',
    isMajorProject: item.isMajorProject || '',
    isFunded: item.isFunded || '',
    flowStatus: item.flowStatus || ''
  }));
  total.value = res.total;
  loading.value = false;
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangePaymentTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ExpenditurePlanVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  proxy.$tab.closePage(route);
  router.push({
    path: `/cssrcFinance/budget/expenditurePlanEdit/index`,
    query: {
      type: 'add'
    }
  });
};

/** 修改按钮操作 */
const handleUpdate = (row?: ExpenditurePlanVO) => {
  proxy.$tab.closePage(route);
  router.push({
    path: `/cssrcFinance/budget/expenditurePlanEdit/index`,
    query: {
      id: row.id.toString(),
      type: 'update'
    }
  });
}

/** 查看按钮操作 */
const handleView = (row?: ExpenditurePlanVO) => {
  proxy.$tab.closePage(route);
  router.push({
    path: `/cssrcFinance/budget/expenditurePlanEdit/index`,
    query: {
      id: row.id.toString(),
      type: 'view'
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ExpenditurePlanVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除财务-预算-支出计划编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delExpenditurePlan(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('cssrcFinance/budget/expenditurePlan/export', {
    ...queryParams.value
  }, `expenditurePlan_${new Date().getTime()}.xlsx`)
}

/** 撤销按钮操作 */
const handleCancelProcessApply = async (id: string) => {
  await proxy?.$modal.confirm('是否确认撤销当前单据？');
  loading.value = true;
  let data = {
    businessId: id,
    message: '申请人撤销流程！'
  };
  await cancelProcessApply(data).finally(() => (loading.value = false));
  await getList();
  proxy?.$modal.msgSuccess('撤销成功');
};

// 自动搜索配置
useAutoSearch({
  watchedFields: ['subjectNumber', 'subjectName', 'deptName', 'nickName', 'isNobalanceProject', 'flowStatus'], // 保持原字段监听
  paramsRef: queryParams, // 主参数对象
  onSearch: handleQuery,
  // 添加额外的监听项：日期选择器的 v-model
  extraRefs: [
    { key: 'dateRangePaymentTime', ref: dateRangePaymentTime }
    // { key: 'dateRange', ref: dateRange }
  ]
});

const handleSortChange = ({ column, order }:{ column:any, prop:string, order: string }) => {
  const prop = column?.property;
  if (!prop) return;
  // 设置排序参数
  queryParams.value.orderColumn = prop;
  queryParams.value.orderType = order === 'ascending' ? 'asc' : 'desc';
  getList();
}

onMounted(async () => {
  await nextTick();
  getList();
});
onBeforeUnmount(() => {
  // 清理组件状态
  loading.value = false;
  expenditurePlanList.value = [];
});
</script>

<style scoped>
  .longlabel {
    align-items: flex-start;
    box-sizing: border-box;
    color: var(--el-text-color-regular);
    display: inline-flex;
    flex: 0 0 auto;
    font-size: var(--el-form-label-font-size);
    justify-content: flex-end;
    padding: 0 12px 0 0;
  }
  ::v-deep .dialog-footer {
    align-items: center;
  }
  ::v-deep .caret-wrapper {
    width: 14px;
  }
</style>
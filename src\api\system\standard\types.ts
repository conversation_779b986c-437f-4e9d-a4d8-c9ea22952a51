export interface StandardVO {
  /**
   * 标准id
   */
  standardId: string;
  /**
   * 标准编号
   */
  standardCode: string;
  /**
   * 标准名称
   */
  standardName: string;
  /**
   * 标准密级
   */
  secret: string;
  /**
   * 标准文件id
   */
  standardFile: number;

  /**
   * 规程状态（0正常 1停用）
   */
  standardStatus: string;

  /**
   * 标准文件名
   */
  standardFileName: string;

  /**
   * 标准文件密级
   */
  standardFileSecret: string;
  /**
   * 标准文件url
   */
  standardFileUrl: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 创建部门
   */
  createDept: string;
  createDeptName: string;
  /**
   * 创建人
   */
  createByNickName: string;
}

export interface StandardForm extends BaseEntity {
  /**
   * 标准id
   */
  standardId?: string;
  /**
   * 标准编号
   */
  standardCode?: string;
  /**
   * 标准名称
   */
  standardName: string;
  /**
   * 标准密级
   */
  secret: string;
  /**
   * 标准文件id
   */
  standardFile?: string;

  /**
   * 规程状态（0正常 1停用）
   */
  standardStatus?: string;

  /**
   * 标准文件名
   */
  standardFileName: string;

  /**
   * 标准文件密级
   */
  standardFileSecret: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 创建部门
   */
  createDept: string;
  createDeptName:string;
  /**
   * 创建人
   */
  createByNickName: string;
}

export interface StandardQuery extends PageQuery {

  /**
   * 标准编号
   */
  standardCode?: string;

  /**
   * 标准文件id
   */
  standardFile?: string;
  /**
   * 标准名称
   */
  standardName: string;
  /**
   * 标准密级
   */
  secret: string;
  /**
   * 标准文件名
   */
  standardFileName: string;
  /**
   * 标准文件密级
   */
  standardFileSecret: string;

  /**
   * 规程状态（0正常 1停用）
   */
  standardStatus?: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 创建部门
   */
  createDept: string;
  createDeptName:string;
  /**
   * 创建人
   */
  createByNickName: string;

  /**
   * 日期范围参数
   */
  params?: any;
}




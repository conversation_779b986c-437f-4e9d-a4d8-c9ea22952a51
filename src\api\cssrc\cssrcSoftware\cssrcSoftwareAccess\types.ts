export interface CssrcSoftwareAccessVO {
  /** 入网审批信息 */
  accessId: string; /** 入网流程ID */
  accessType: string;/** 入网类型 */
  accessKind: string; /** 入网申请 */
  accessResponsible: string | number; /** 责任人ID */
  accessResponsibleName: string; /** 责任人工号 */
  accessResponsibleNickName: string; /** 责任人姓名 */
  networkType: string; // 计算机联网类型
  computerCode: string; // 计算机密级编号
  flowStatus: string; // 流程状态
  createDept: string; // 创建部门ID
  createDeptName: string; // 创建部门名称
  createBy: string; // 创建者ID
  createByName: string; // 创建者工号
  createByNickName: string; // 创建者姓名
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  /** 软件信息 */
  softwareId: string | number;
  categoryId: string | number; // 软件分类ID
  categoryName: string; // 软件分类名称
  softwareName: string; // 软件名称
  manufacturer: string; // 生产厂商
  country: string; // 生产国别
  intro: string; // 软件简介
  secret: string; // 软件密级
  /** 软件版本信息 */
  versionId: string;
  versionName: string; // 版本号（如 v1.0.0）
  platform: string; // 平台（windows/linux）
  architecture: string; // 架构（x86/x64/arm64）
  packageType: string; // 包类型（exe/msi/deb/rpm/tar.gz）
  bits: string; // 位数（32/64）
  fileSize: string; // 文件大小(GB)
}

export interface CssrcSoftwareAccessForm extends BaseEntity {
  /**
   * ID
   */
  accessId: string;
  /**
   * 入网类型
   */
  accessType?: string;
  accessKind: string; /** 入网申请 */

  /**
   * 责任人
   */
  accessResponsible?: string | number;
  accessResponsibleNickName: string;

  /**
   * 计算机联网类型
   */
  networkType?: string;

  /**
   * 计算机密级编号
   */
  computerCode?: string;

  /**
   * 流程状态
   */
  flowStatus?: string;

  /**
   * 创建部门
   */
  createDept?: string;

  /**
   * 创建者
   */
  createBy?: string;
  createByNickName: string;

  /**
   * 创建时间
   */
  createTime: string;

  /** 软件信息 */
  softwareId: string | number;
  categoryId: string | number; // 软件分类ID
  categoryName: string; // 软件分类名称
  softwareName: string; // 软件名称
  manufacturer: string; // 生产厂商
  country: string; // 生产国别
  intro: string; // 软件简介
  secret: string; // 软件密级
  /** 软件版本信息 */
  versionName: string; // 版本号（如 v1.0.0）
  platform: string; // 平台（windows/linux）
  architecture: string; // 架构（x86/x64/arm64）
  packageType: string; // 包类型（exe/msi/deb/rpm/tar.gz）
  bits: string; // 位数（32/64）
  fileSize: string; // 文件大小(GB)
}

export interface CssrcSoftwareAccessQuery extends PageQuery {
  /**
   * ID
   */
  accessId: string;
  /**
   * 入网类型
   */
  accessType?: string;
  accessKind: string; /** 入网申请 */

  /**
   * 责任人
   */
  accessResponsible?: string | number; 
  accessResponsibleNickName: string;

  /**
   * 计算机联网类型
   */
  networkType?: string;

  /**
   * 计算机密级编号
   */
  computerCode?: string;

  /**
   * 流程状态
   */
  flowStatus?: string;

  /**
   * 创建部门
   */
  createDept?: string;

  /**
   * 创建者
   */
  createBy?: string;
  createByNickName: string; // 创建者姓名

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  /** 软件信息 */
  softwareId: string | number;
  categoryId: string | number; // 软件分类ID
  categoryName: string; // 软件分类名称
  softwareName: string; // 软件名称
  manufacturer: string; // 生产厂商
  country: string; // 生产国别
  intro: string; // 软件简介
  secret: string; // 软件密级
  /** 软件版本信息 */
  versionName: string; // 版本号（如 v1.0.0）
  platform: string; // 平台（windows/linux）
  architecture: string; // 架构（x86/x64/arm64）
  packageType: string; // 包类型（exe/msi/deb/rpm/tar.gz）
  bits: string; // 位数（32/64）
  fileSize: string; // 文件大小(GB)
}




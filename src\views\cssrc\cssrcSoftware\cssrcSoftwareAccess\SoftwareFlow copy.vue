<!-- 添加或修改软件基本信息对话框 -->
<template>
<div>
    <div class="p-2">
        <el-card shadow="never" style="border: 0px;">
            <div style="display: flex; justify-content: space-between">
                <div>
                    <el-button v-if="submitButtonShow" :loading="buttonLoading" type="info" @click="submitForm('draft')">暂存</el-button>
                    <el-button v-if="submitButtonShow" :loading="buttonLoading" type="primary" @click="submitForm('submit')">提交</el-button>
                    <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary" @click="approvalVerifyOpen">审批</el-button>
                    <el-button v-if="form && form.softwareId && form.flowStatus !== 'draft'" type="primary" @click="handleApprovalRecord">流程进度</el-button>
                </div>
                <div>
                    <el-button style="float: right" @click="goBack()">返回</el-button>
                </div>
            </div>
        </el-card>
        <el-card shadow="never" style="height: 82vh; border: 0px; overflow-y: auto">
            <el-form ref="cssrcSoftwareInfoFormRef" v-loading="loading" :disabled="routeParams.type === 'view' || routeParams.type === 'approval'" :model="form" :rules="rules" label-width="80px">
                <el-row :gutter="20">
                    <el-col :span="10">
                        <el-form-item label="软件分类" prop="categoryId">
                        <el-tree-select
                            v-model="form.categoryId"
                            :data="enabledCategoryOptions"
                            :props="{ value: 'id', label: 'label', children: 'children' } as any"
                            value-key="id"
                            placeholder="请选择软件分类"
                            check-strictly
                            default-expand-all
                            style="width: 100%"
                        />
                        </el-form-item>
                    </el-col>
                    <el-col :span="10">
                        <el-form-item label="软件名称" prop="softwareName">
                        <el-input v-model="form.softwareName" placeholder="请输入软件名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">                   
                    <el-col :span="12">
                        <el-form-item label="生产厂商" prop="manufacturer">
                            <el-input v-model="form.manufacturer" placeholder="请输入生产厂商" />
                        </el-form-item>
                    </el-col>
                    </el-row>
                    <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="生产国别" prop="country">
                        <el-input v-model="form.country" placeholder="请输入生产国别" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="密级" prop="secret">
                        <el-select v-model="form.secret" placeholder="请选择密级" >
                            <el-option
                            v-for="dict in filteredSysFileSecret"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                            ></el-option>
                        </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="软件简介" prop="intro">
                        <el-input v-model="form.intro" type="textarea" placeholder="请输入软件简介" :rows="3" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 修改时显示分类名称和下载次数 -->
                <el-row :gutter="20" v-if="form.softwareId">
                <el-col :span="12">
                    <el-form-item label="分类名称">
                    <el-input v-model="form.categoryName" disabled />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="下载次数">
                    <el-input v-model="form.downloadCount" disabled />
                    </el-form-item>
                </el-col>
                </el-row>
            </el-form>
        </el-card>
        <!-- 提交组件 -->
        <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" @submit-callback="submitCallback" />
        <!-- 审批记录 -->
        <approvalRecord ref="approvalRecordRef" />
    </div>
</div>
</template>
<script setup name="Notice" lang="ts">
import { listCssrcSoftwareInfo, getCssrcSoftwareInfo, delCssrcSoftwareInfo, addCssrcSoftwareInfo, updateCssrcSoftwareInfo } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo';
import { CssrcSoftwareInfoVO, CssrcSoftwareInfoQuery, CssrcSoftwareInfoForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo/types';
import { CssrcSoftwareCategoryVO, CssrcSoftwareCategoryTreeVO } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/types';
import { categoryTreeSelect } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/index';
import { startWorkFlow } from '@/api/workflow/task';
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import { AxiosResponse } from 'axios';
import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable, sys_file_secret, wf_business_status } = toRefs<any>(proxy?.useDict('sys_normal_disable', 'sys_file_secret', 'wf_business_status'));
// 过滤 sys_file_secret 字典数据
const filteredSysFileSecret = computed(() => {
    return sys_file_secret.value.filter((dict: any) => dict.value === '0' || dict.value === '5' || dict.value === '10');
});
const categoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);
const enabledCategoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);

/* 审批组件相关定义 */
const buttonLoading = ref(false);
const loading = ref(true);
const routeParams = ref<Record<string, any>>({}); // 路由参数
const flowCode = ref<string>('');
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>(); // 提交组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>(); // 审批记录组件
const cssrcSoftwareInfoFormRef = ref<ElFormInstance>();
const submitFormData = ref<StartProcessBo>({
    businessId: '',
    flowCode: '',
    variables: {}
});
const taskVariables = ref<Record<string, any>>({});

const initFormData: CssrcSoftwareInfoForm = {
    softwareId: undefined,
    categoryId: undefined,
    softwareName: undefined,
    secret: undefined,
    categoryName: undefined,
    manufacturer: undefined,
    country: undefined,
    intro: undefined,
    status: undefined,
    downloadCount: undefined,
    createBy: undefined,
    flowStatus: '',
}
const data = reactive<PageData<CssrcSoftwareInfoForm, CssrcSoftwareInfoQuery>>({
    form: {...initFormData},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryId: undefined,
        categoryName: undefined,
        softwareName: undefined,
        manufacturer: undefined,
        country: undefined,
        intro: undefined,
        secret: undefined,
        status: undefined,
        downloadCount: undefined,
        createBy: undefined,
        flowStatus: '',
        params: {
        }
    },
    rules: {
        softwareId: [
        { required: true, message: "软件ID不能为空", trigger: "blur" }
        ],
        categoryId: [
        { required: true, message: "软件分类ID不能为空", trigger: "blur" }
        ],
        softwareName: [
        { required: true, message: "软件名称不能为空", trigger: "blur" }
        ],
    }
});

const { form, rules } = toRefs(data);

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData };
    cssrcSoftwareInfoFormRef.value?.resetFields();
};

/** 查询软件分类下拉树结构 */
const getCategoryTree = async () => {
const res = await categoryTreeSelect();
  categoryOptions.value = res.data; // 树结构
  enabledCategoryOptions.value = filterDisabledCategory(res.data); // 也是树结构
};

/** 过滤禁用的软件分类 */
const filterDisabledCategory = (categoryList: CssrcSoftwareCategoryTreeVO[]): CssrcSoftwareCategoryTreeVO[] => {
    return categoryList.filter((category) => {
        if (category.disabled) {
        return false;
        }
        if (category.children && category.children.length) {
        category.children = filterDisabledCategory(category.children);
        }
        return true;
    });
};

/** 获取详情 */
const getInfo = () => {
    loading.value = true;
    buttonLoading.value = false;
    nextTick(async () => {
        const res = await getCssrcSoftwareInfo(routeParams.value.id);
        Object.assign(form.value, res.data);
        loading.value = false;
        buttonLoading.value = false;
    });
};

/** 提交按钮 */
const submitForm = (flowStatus: string) => {
    if (!form.value) {
        ElMessage.error('表单数据无效，请刷新页面重新尝试');
        return;
    }
    try {
        cssrcSoftwareInfoFormRef.value?.validate(async (valid: boolean) => {
            if (valid) {
                buttonLoading.value = true;
                let res: AxiosResponse<CssrcSoftwareInfoVO>;
                if (form.value.softwareId) {
                    res = await updateCssrcSoftwareInfo(form.value);
                } else {
                    res = await addCssrcSoftwareInfo(form.value);
                }
                if (!res.data) {
                    ElMessage.error('操作失败，请稍后再试');
                    buttonLoading.value = false;
                    return;
                }
                form.value = res.data;
                if (flowStatus === 'draft') {
                    buttonLoading.value = false;
                    proxy?.$modal.msgSuccess('暂存成功');
                    proxy.$tab.closePage(route);
                    router.go(-1);
                } else {
                    flowCode.value = 'cssrcSoftwareAccess';
                    await handleStartWorkFlow(res.data); // 启动工作流
                }
            }
        });
    }
    catch (error) {
        ElMessage.error('提交失败，请稍后再试');
        console.error(error);
    }
    finally {
        buttonLoading.value = false;
    }
}
/** 提交申请 */
const handleStartWorkFlow = async (data: CssrcSoftwareInfoForm) => {
    try {
        if (!data || !data.softwareId) {
            throw new Error('Invalid data or missing softwareId');
        }
        submitFormData.value.flowCode = flowCode.value;
        submitFormData.value.businessId = data.softwareId;
        // 工作流启动时传递的任务变量
        taskVariables.value = {
            // userList: ['1']
        };
        submitFormData.value.variables = taskVariables.value;
        const resp = await startWorkFlow(submitFormData.value);
        if (submitVerifyRef.value) {
            buttonLoading.value = false;
            submitVerifyRef.value.openDialog(resp.data.taskId);
        }
    }
    finally {
        buttonLoading.value = false;
    }
};
//审批记录
const handleApprovalRecord = () => {
    approvalRecordRef.value.init(form.value.softwareId);
};
//提交回调
const submitCallback = async () => {
    await proxy.$tab.closePage(route);
    router.go(-1);
};
//返回
const goBack = () => {
    proxy.$tab.closePage(route);
    router.go(-1);
};
//审批
const approvalVerifyOpen = async () => {
    submitVerifyRef.value.openDialog(routeParams.value.taskId);
};
//校验提交按钮是否显示
const submitButtonShow = computed(() => {
    return (
        routeParams.value.type === 'add' ||
        (routeParams.value.type === 'update' &&
            form.value.flowStatus &&
            (form.value.flowStatus === 'draft' || form.value.flowStatus === 'cancel' || form.value.flowStatus === 'back')
        )
    );
});
//校验审批按钮是否显示
const approvalButtonShow = computed(() => {
    return routeParams.value.type === 'approval' && form.value.flowStatus && form.value.flowStatus === 'waiting';
});

onMounted(() => {
    nextTick(async () => {
        routeParams.value = route.query;
        reset();
        loading.value = false;
        if (routeParams.value.type === 'update' || routeParams.value.type === 'view' || routeParams.value.type === 'approval') {
            getInfo();
        }
    });
    getCategoryTree();
});
</script>
<style scoped lang="scss"></style>
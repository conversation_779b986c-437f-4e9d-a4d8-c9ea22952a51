<!-- 添加或修改财务-预算-支出计划对话框 -->
<template>
<div>
    <div class="p-2">
        <el-card shadow="never" style="border: 0px;">
            <div style="display: flex; justify-content: space-between">
                <div>
                    <el-button v-if="submitButtonShow" :loading="buttonLoading" type="info" @click="submitForm('draft')">暂存</el-button>
                    <el-button v-if="submitButtonShow" :loading="buttonLoading" type="primary" @click="submitForm('submit')">提交</el-button>
                    <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary" @click="approvalVerifyOpen">审批</el-button>
                    <el-button v-if="form && form.id && form.flowStatus !== 'draft'" type="primary" @click="handleApprovalRecord">流程进度</el-button>
                </div>
                <div>
                    <el-button style="float: right" @click="goBack()">返回</el-button>
                </div>
            </div>
        </el-card>
        <el-card shadow="never" style="height: 82vh; border: 0px; overflow-y: auto">
            <el-form ref="expenditurePlanFormRef" 
            v-loading="loading" :disabled="routeParams.type === 'view' || routeParams.type === 'approval'"
            :model="form" :rules="rules" label-width="110px">
            <el-row>
                <el-col :span="10">
                <el-form-item label="课题编号" prop="subjectNumber">
                    <el-input v-model="form.subjectNumber" placeholder="请输入课题编号" />
                </el-form-item>
                </el-col>
                <el-col :span="10">
                <el-form-item label="课题名称" prop="subjectName">
                    <el-input v-model="form.subjectName" placeholder="请输入课题名称" />
                </el-form-item>
                </el-col>
                <el-col :span="10">
                <el-form-item label="项目类别" prop="projectType">
                    <el-select v-model="form.projectType" placeholder="请选择项目类别" clearable>
                        <el-option value="科研项目" label="科研项目"/>
                        <el-option value="军品销售" label="军品销售"/>
                        <el-option value="民品销售" label="民品销售"/>
                        <el-option value="四技" label="四技"/>
                        <el-option value="其他" label="其他"/>
                    </el-select>
                </el-form-item>
                </el-col>
                <el-col :span="10"></el-col>
                <el-col :span="10">
                <el-form-item label="申请人" prop="nickName">
                    <el-input v-model="form.nickName" style="width:258px; padding-right:10px;" readonly/>
                    <el-button type="primary" plain icon="Plus" @click="openUserSelect">选择申请人</el-button>
                </el-form-item>
                </el-col>
                <el-col :span="10">
                <el-form-item label="申请人部门" prop="deptName">
                    <el-input v-model="form.deptName" :value="form.deptName" readonly/>
                </el-form-item>
                </el-col>
                <el-col :span="10">
                <el-form-item label="支出类别" prop="expenditureType">
                    <el-select v-model="form.expenditureType" placeholder="请选择支出类别" clearable>
                    <el-option value="材料费" label="材料费"/>
                    <el-option value="专用费" label="专用费"/>
                    <el-option value="外协费" label="外协费"/>
                    <el-option value="分拨款" label="分拨款"/>
                    </el-select>
                </el-form-item>
                </el-col>
                <el-col :span="20">
                <el-form-item label="支出内容" prop="content">
                    <el-input v-model="form.content" type="textarea" placeholder="请输入支出内容" />
                </el-form-item>
                </el-col>
                <el-col :span="10">
                <el-form-item label="合同总金额" prop="contractTotalAmount">
                    <el-input 
                        v-model="form.contractTotalAmount" 
                        placeholder="请输入合同总金额" 
                        :formatter="(value) => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="(value) => value.replace(/\￥\s?|(,*)/g,'')"
                        @input="expenditurePlanFormRef?.validateField('contractTotalAmount')">
                        <template #append>万元</template>
                    </el-input>
                </el-form-item>
                </el-col>
                <el-col :span="10">
                <el-form-item label="本次付款金额" prop="currentPaymentAmount">
                    <el-input 
                        v-model="form.currentPaymentAmount" 
                        placeholder="请输入本次付款金额" 
                        :formatter="(value) => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="(value) => value.replace(/\￥\s?|(,*)/g,'')"
                        @input="expenditurePlanFormRef?.validateField('currentPaymentAmount')">
                        <template #append>万元</template>
                    </el-input>
                </el-form-item>
                </el-col>
                <el-col :span="20">
                <el-form-item label="付款时间" prop="paymentTime">
                    <el-date-picker
                    class="picker-input"
                    clearable
                    v-model="form.paymentTime"
                    type="month"
                    value-format="YYYY-MM"
                    :disabled-date="disabledDate"
                    placeholder="请选择付款时间">
                    </el-date-picker>
                </el-form-item>
                </el-col>
                <el-col :span="7">
                <el-form-item label="是否零余额项目" prop="isNobalanceProject">
                    <el-radio-group v-model="form.isNobalanceProject">
                    <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                </el-col>
                <el-col :span="7">
                <el-form-item label="是否重点项目" prop="isMajorProject">
                    <el-radio-group v-model="form.isMajorProject">
                    <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                </el-col>
                <el-col :span="7">
                <el-form-item label="项目是否到款" prop="isFunded">
                    <el-radio-group v-model="form.isFunded">
                    <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                </el-col>
                <el-col :span="10">
                <el-form-item label="支付方式" prop="paymentMethod">
                    <el-select v-model="form.paymentMethod" placeholder="请选择支付方式" clearable>
                        <el-option value="银行转账" label="银行转账"/>
                        <el-option value="银行承兑" label="银行承兑"/>
                    </el-select>
                </el-form-item>
                </el-col>
            </el-row>
            </el-form>
            <UserSelect ref="userSelectRef" :multiple="true" :data="selectUserId" @confirm-call-back="userSelectCallBack"></UserSelect>
        </el-card>
        <!-- 提交组件 -->
        <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" @submit-callback="submitCallback" />
        <!-- 审批记录 -->
        <approvalRecord ref="approvalRecordRef" />
    </div>
</div>
</template>
<script setup name="ExpenditurePlan" lang="ts">
    import { addExpenditurePlan, getExpenditurePlan, updateExpenditurePlan } from '@/api/cssrcFinance/budget/expenditurePlan';
    import { ExpenditurePlanVO, ExpenditurePlanQuery, ExpenditurePlanForm } from '@/api/cssrcFinance/budget/expenditurePlan/types';
    import { UserVO } from '@/api/system/user/types';
    import dayjs from 'dayjs';
    import UserSelect from '@/components/UserSelect/index.vue';
    import { startWorkFlow } from '@/api/workflow/task';
    import SubmitVerify from '@/components/Process/submitVerify.vue';
    import ApprovalRecord from '@/components/Process/approvalRecord.vue';
    import { AxiosResponse } from 'axios';
    import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
    import { useRouter, useRoute } from 'vue-router';

    const router = useRouter();
    const route = useRoute();
    const routeParams = ref<Record<string, any>>({}); // 路由参数
    const flowCode = ref<string>('');
    const { proxy } = getCurrentInstance() as ComponentInternalInstance;
    const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));
    const buttonLoading = ref(false);
    const loading = ref(true);
    const expenditurePlanFormRef = ref<ElFormInstance>();
    // 定义申请人选择组件
    const userSelectRef = ref<InstanceType<typeof UserSelect>>();
    // 申请人id
    const selectUserId = ref<number | string>();
    // 申请人部门id
    const selectDeptId = ref<number | string>();
    //提交组件
    const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>();
    //审批记录组件
    const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>();
    const submitFormData = ref<StartProcessBo>({
        businessId: '',
        flowCode: '',
        variables: {}
    });
    const taskVariables = ref<Record<string, any>>({});

    const initFormData: ExpenditurePlanForm = {
        id: undefined,
        deptId: '',
        deptName: '',
        userId: '',
        userName: '',
        nickName: '',
        expenditureType: '',
        content: '',
        subjectNumber: '',
        subjectName: '',
        projectType: '',
        contractTotalAmount: undefined,
        currentPaymentAmount: undefined,
        paymentTime: '',
        isNobalanceProject: '',
        isMajorProject: '',
        isFunded: '',
        paymentMethod: '',
        flowStatus: ''
    }
    const data = reactive<PageData<ExpenditurePlanForm, ExpenditurePlanQuery>>({
        form: {...initFormData},
        queryParams: {
            pageNum: 1,
            pageSize: 10,
            deptName: '',
            userId: '',
            userName: '',
            nickName: '',
            expenditureType: '',
            subjectNumber: '',
            subjectName: '',
            projectType: '',
            isNobalanceProject: '',
            params: {
                paymentTime: '',
            },
            flowStatus: ''
        },
        rules: {
            id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
            expenditureType: [
                { required: true, message: "支出类别不能为空", trigger: "change" }
            ],
            subjectNumber: [
                { required: true, message: "课题编号不能为空", trigger: "blur" }
            ],
            subjectName: [
                { required: true, message: "课题名称不能为空", trigger: "blur" }
            ],
            projectType: [
                { required: true, message: "项目类别不能为空", trigger: "change" }
            ],
            nickName: [
                { required: true, message: "申请人不能为空", trigger: "blur" }
            ],
            contractTotalAmount: [
                { required: true, message: "合同总金额不能为空", trigger: "blur" },
                {
                    validator: (rule:any, value:string, callback: any) => {
                        const rawValue = value.replace(/￥\s?|,/g, ''); // 使用parser获取原始值
                        // 允许空值由required规则处理
                        if (rawValue === '') {
                            callback();
                            return;
                        }
                        // 验证数字格式
                        const numberRegex = /^-?\d+(\.\d{0,6})?$/;
                        if (!numberRegex.test(value)) {
                            callback(new Error('只能输入数字且最多保留6位小数'));
                        } else {
                            callback();
                        }
                    },
                    trigger: 'input' // 实时触发验证
                }
                // { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: "金额格式错误(小数点后最多两位)", trigger: "blur" }
            ],
            currentPaymentAmount: [
                { required: true, message: "本次付款金额不能为空", trigger: "blur" },
                {
                    validator: (rule:any, value:string, callback: any) => {
                        const rawValue = value.replace(/￥\s?|,/g, ''); // 使用parser获取原始值
                        // 允许空值由required规则处理
                        if (rawValue === '') {
                            callback();
                            return;
                        }
                        // 验证数字格式
                        const numberRegex = /^-?\d+(\.\d{0,6})?$/;
                        if (!numberRegex.test(value)) {
                            callback(new Error('只能输入数字且最多保留6位小数'));
                        } else {
                            callback();
                        }
                    },
                    trigger: 'input' // 实时触发验证
                }
                // { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: "金额格式错误(小数点后最多两位)", trigger: "blur" }
            ],
            paymentTime: [{ required: true, message: '付款时间不能为空', trigger: 'blur' }],
        }
    });
    const { form, rules } = toRefs(data);

    /** 日期选择器判断付款月份逻辑 */
    const disabledDate = (time:Date) => {
        const now = new Date();
        // 获取当前月份和日期
        const currentDate = now.getDate();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        // 计算最小允许的月份
        let minAllowedMonth = currentMonth + 1;
        let minAllowedYear = currentYear;
        // 如果当前日期超过17号，则允许下下个月
        if (currentDate > 17) {
            minAllowedMonth += 1;
            // 处理跨年份逻辑
            if (minAllowedMonth > 11) {
                minAllowedMonth = 0;
                minAllowedYear += 1;
            }
        }
        // 构造最小允许的月份首日
        const minAllowedDate = new Date(Date.UTC(minAllowedYear, minAllowedMonth, 1));
        // 禁用早于最小允许月份的日期
        return time.getTime() < minAllowedDate.getTime();
    }

    /** 打开申请人选择弹窗 */
    const openUserSelect = () => {
    userSelectRef.value.open({
        deptParam: 'unit' // company,department,unit
    });
    };
    //确认选择申请人
    const userSelectCallBack = (data: UserVO[]) => {
        if (data && data.length === 1) {
            const selectedUser = data[0];
            selectUserId.value = selectedUser.userId;
            selectDeptId.value = selectedUser.deptId;
            form.value.nickName = selectedUser.nickName;
            form.value.deptName = selectedUser.deptName;
        }
    };

    /** 表单重置 */
    const reset = () => {
        form.value = {...initFormData};
        expenditurePlanFormRef.value?.resetFields();
    }
    /** 获取详情 */
    const getInfo = () => {
        loading.value = true;
        buttonLoading.value = false;
        nextTick(async () => {
            const res = await getExpenditurePlan(routeParams.value.id);
            Object.assign(form.value, res.data);
            loading.value = false;
            buttonLoading.value = false;
        });
    };
    /** 提交按钮 */
    const submitForm = (flowStatus: string) => {
        if (!form.value) {  // 检查 form 是否为 null 或 undefined
            ElMessage.error('表单数据无效，请刷新页面重新尝试');
            return;
        }
        try {
            expenditurePlanFormRef.value?.validate(async (valid: boolean) => {
                form.value.userId = selectUserId.value;
                form.value.deptId = selectDeptId.value;
                // 对比本次付款金额不得大于合同总金额
                const contractTotalAmountValue = Number((form.value.contractTotalAmount + '').replace(/￥\s?|,/g, ''));
                const currentPaymentAmountValue = Number((form.value.currentPaymentAmount + '').replace(/￥\s?|,/g, ''));
                if (contractTotalAmountValue < currentPaymentAmountValue) {
                    ElMessage.error('本次付款金额不得大于合同总金额');
                    buttonLoading.value = false;
                    return;
                }
                // 格式化日期字段，默认12:00:00提交
                form.value.paymentTime = dayjs(form.value.paymentTime).format('YYYY-MM-17 12:00:00');
                if (valid) {
                    buttonLoading.value = true;
                    let res: AxiosResponse<ExpenditurePlanVO>;
                    if (form.value.id) {
                        res = await updateExpenditurePlan(form.value);
                    } else {
                        res = await addExpenditurePlan(form.value);
                    }
                    // 检查 res.data 是否有效
                    if (!res.data) {
                        ElMessage.error('操作失败，请稍后再试');
                        buttonLoading.value = false;
                        return;
                    }
                    form.value = res.data;
                    if (flowStatus === 'draft') {
                        buttonLoading.value = false;
                        proxy?.$modal.msgSuccess('暂存成功');
                        proxy.$tab.closePage(route);
                        router.go(-1);
                    } else {
                        flowCode.value = 'expenditurePlan';
                        await handleStartWorkFlow(res.data); // 启动工作流
                    }
                }
            })
        }
        catch (error) {
            ElMessage.error('提交失败，请稍后再试');
            console.error(error);
        }
        finally {
            buttonLoading.value = false;
        }
    };
    /** 提交申请 */
    const handleStartWorkFlow = async (data: ExpenditurePlanForm) => {
        try {
            if (!data || !data.id) {
                throw new Error('Invalid data or missing Id');
            }
            submitFormData.value.flowCode = flowCode.value;
            submitFormData.value.businessId = data.id;
            // 工作流启动时传递的任务变量
            taskVariables.value = {
                // userList: ['1']
            };
            submitFormData.value.variables = taskVariables.value;
            const resp = await startWorkFlow(submitFormData.value);
            if (submitVerifyRef.value) {
                buttonLoading.value = false;
                submitVerifyRef.value.openDialog(resp.data.taskId);
            }
        }
        finally {
            buttonLoading.value = false;
        }
    };

    //审批记录
    const handleApprovalRecord = () => {
        approvalRecordRef.value.init(form.value.id);
    };
    //提交回调
    const submitCallback = async () => {
        await proxy.$tab.closePage(route);
        router.go(-1);
    };
    //返回
    const goBack = () => {
        proxy.$tab.closePage(route);
        router.go(-1);
    };
    //审批
    const approvalVerifyOpen = async () => {
        submitVerifyRef.value.openDialog(routeParams.value.taskId);
    };

    /** 校验提交按钮是否显示 */
    const submitButtonShow = computed(() => {
        return (
            routeParams.value.type === 'add' ||
            (routeParams.value.type === 'update' &&
                form.value.flowStatus &&
                (form.value.flowStatus === 'draft' || form.value.flowStatus === 'cancel' || form.value.flowStatus === 'back')
            )
        );
    });
    /** 校验审批按钮是否显示 */
    const approvalButtonShow = computed(() => {
        return routeParams.value.type === 'approval' && form.value.flowStatus && form.value.flowStatus === 'waiting';
    });

    onMounted(() => {
        nextTick(async () => {
            routeParams.value = route.query;
            reset();
            loading.value = false;
            if (routeParams.value.type === 'update' || routeParams.value.type === 'view' || routeParams.value.type === 'approval') {
                getInfo();
            }
        });
    });
</script>
<style scoped>
    /* 使用css穿透修改输入框内部输入区域 */
    ::v-deep .picker-input .el-input__inner {
        width:539px;
    }
</style>
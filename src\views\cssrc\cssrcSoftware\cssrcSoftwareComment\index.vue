<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="版本ID" prop="versionId">
              <el-input v-model="queryParams.versionId" placeholder="请输入版本ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['cssrc:cssrcSoftwareComment:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['cssrc:cssrcSoftwareComment:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['cssrc:cssrcSoftwareComment:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['cssrc:cssrcSoftwareComment:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="cssrcSoftwareCommentList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="评论ID" align="center" prop="commentId" v-if="true" />
        <el-table-column label="版本ID" align="center" prop="versionId" />
        <el-table-column label="用户工号" align="center" prop="userName" />
        <el-table-column label="用户姓名" align="center" prop="nickName" />
        <el-table-column label="评论内容" align="center" prop="content" />
        <el-table-column label="回复哪条评论" align="center" prop="replyTo" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareComment:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareComment:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改软件评论对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="cssrcSoftwareCommentFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="版本ID" prop="versionId">
          <el-input v-model="form.versionId" placeholder="请输入版本ID" />
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="评论内容">
          <editor v-model="form.content" :min-height="192"/>
        </el-form-item>
        <el-form-item label="回复哪条评论" prop="replyTo">
          <el-input v-model="form.replyTo" placeholder="请输入回复哪条评论" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CssrcSoftwareComment" lang="ts">
import { listCssrcSoftwareComment, getCssrcSoftwareComment, delCssrcSoftwareComment, addCssrcSoftwareComment, updateCssrcSoftwareComment } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareComment';
import { CssrcSoftwareCommentVO, CssrcSoftwareCommentQuery, CssrcSoftwareCommentForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareComment/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const cssrcSoftwareCommentList = ref<CssrcSoftwareCommentVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const cssrcSoftwareCommentFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CssrcSoftwareCommentForm = {
  commentId: undefined,
  versionId: undefined,
  userId: undefined,
  content: undefined,
  replyTo: undefined,
  status: undefined,
}
const data = reactive<PageData<CssrcSoftwareCommentForm, CssrcSoftwareCommentQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    versionId: undefined,
    userId: undefined,
    content: undefined,
    replyTo: undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
    commentId: [
      { required: true, message: "评论ID不能为空", trigger: "blur" }
    ],
    versionId: [
      { required: true, message: "版本ID不能为空", trigger: "blur" }
    ],
    userId: [
      { required: true, message: "用户ID不能为空", trigger: "blur" }
    ],
    content: [
      { required: true, message: "评论内容不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询软件评论列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCssrcSoftwareComment(queryParams.value);
  cssrcSoftwareCommentList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  cssrcSoftwareCommentFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CssrcSoftwareCommentVO[]) => {
  ids.value = selection.map(item => item.commentId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加软件评论";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: CssrcSoftwareCommentVO) => {
  reset();
  const _commentId = row?.commentId || ids.value[0]
  const res = await getCssrcSoftwareComment(_commentId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改软件评论";
}

/** 提交按钮 */
const submitForm = () => {
  cssrcSoftwareCommentFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.commentId) {
        await updateCssrcSoftwareComment(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addCssrcSoftwareComment(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: CssrcSoftwareCommentVO) => {
  const _commentIds = row?.commentId || ids.value;
  await proxy?.$modal.confirm('是否确认删除软件评论编号为"' + _commentIds + '"的数据项？').finally(() => loading.value = false);
  await delCssrcSoftwareComment(_commentIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('cssrc/cssrcSoftwareComment/export', {
    ...queryParams.value
  }, `cssrcSoftwareComment_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>

import { Ref, watch } from 'vue';

// 每个 ExtraRef 对象包含 key（唯一标识）和 ref（响应式变量）。
interface ExtraRef {
    key: string; // 唯一标识符，用于合并到依赖项中
    ref: Ref<any>; // 需要监听的响应式变量
}

interface WatchOptions {
    watchedFields: string[]; // 需要监听的 paramsRef 中的字段
    paramsRef: Ref<any>; // 主要监听的参数对象
    extraRefs?: ExtraRef[]; // 额外需要监听的日期等时间对象
    onSearch: () => void;
    debounceTime?: number; 
    resetCondition?: (newValues: Record<string, any>) => boolean;
}

/**
 * 自动化搜索监听器，处理防抖和参数变化检测
 *
 * @param watchedFields 需要监听的字段列表（如：['username', 'email']）
 * @param extraRefs 需要监听的模板中独立的 v-model 变量（如日期选择器）
 * @param onSearch 参数变化时触发的搜索函数
 */
export default function useAutoSearch({
    watchedFields,
    paramsRef,
    onSearch,
    extraRefs = [],
    debounceTime = 300,
}: WatchOptions): void {
    // 防抖函数
    const debouncedSearch = (() => {
        let timer: NodeJS.Timeout;
        return (...args) => {
            clearTimeout(timer);
            timer = setTimeout(() => onSearch(...args), debounceTime);
        };
    })();

    // 合并监听依赖项：paramsRef 的字段 + extraRefs 的值
    watch(
        computed(() => {
            // 1. 提取 paramsRef 中的字段值
            const paramsValues = watchedFields.reduce<Record<string, any>>(
            (acc, field) => {
                acc[field] = paramsRef.value[field];
                return acc;
            },
            {}
            );

            // 2. 提取 extraRefs 的值
            const extraValues = extraRefs.reduce<Record<string, any>>(
                (acc, { key, ref }) => {
                    acc[key] = ref.value;
                    return acc;
                },
                {}
            );

            // 3. 合并所有依赖项
            return { ...paramsValues, ...extraValues };
    }),
    (newValues) => {
        // 触发防抖搜索
        debouncedSearch();
    },
    { deep: true }
    );
}
<template>
    <div>
        <el-dialog v-model="softwareDialog.visible.value" :title="softwareDialog.title.value" width="80%" append-to-body>
        <el-row :gutter="20">
            <!-- 软件分类树 -->
            <el-col :lg="4" :xs="24">
            <el-card shadow="hover">
                <el-input v-model="categoryName" placeholder="请输入分类名称" prefix-icon="Search" clearable />
                <el-tree
                ref="categoryTreeRef"
                class="mt-2"
                node-key="id"
                :data="categoryOptions"
                :props="{ label: 'label', children: 'children' } as any"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                highlight-current
                default-expand-all
                @node-click="handleNodeClick"
                />
            </el-card>
            </el-col>
            
            <!-- 软件列表 -->
            <el-col :lg="20" :xs="24">
            <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                <div v-show="showSearch" class="mb-[10px]">
                    <el-card shadow="hover">
                        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                        <el-form-item label="软件名称" prop="softwareName">
                            <el-input 
                            v-model="queryParams.softwareName" 
                            placeholder="请输入软件名称" 
                            clearable 
                            @keyup.enter="handleQuery" 
                            />
                        </el-form-item>
                        </el-form>
                    </el-card>
                </div>
            </transition>

            <el-card shadow="hover">
                <template v-if="prop.multiple" #header>
                <el-tag 
                    v-for="software in selectSoftwareList" 
                    :key="software.softwareId" 
                    closable 
                    style="margin: 2px" 
                    @close="handleCloseTag(software)"
                >
                    {{ software.softwareName }}
                </el-tag>
                </template>

                <vxe-table
                ref="tableRef"
                height="400px"
                border
                show-overflow
                :data="softwareList"
                :loading="loading"
                :row-config="{ keyField: 'id', isHover: true }"
                :checkbox-config="{ reserve: true, trigger: 'row', highlight: true, showHeader: prop.multiple }"
                @checkbox-all="handleCheckboxAll"
                @checkbox-change="handleCheckboxChange"
                >
                <vxe-column type="checkbox" width="50" align="center" />
                <vxe-column key="softwareName" title="软件名称" align="center" field="softwareName" />
                <vxe-column key="manufacturer" title="生产厂商" align="center" field="manufacturer" />
                <vxe-column key="country" title="生产国别" align="center" field="country" />
                <vxe-column key="secret" title="软件密级" align="center" field="secret" >
                    <template #default="scope">
                        <dict-tag :options="sys_file_secret" :value="scope.row.secret"/>
                    </template>
                </vxe-column>
                </vxe-table>

                <pagination
                v-show="total > 0"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                :total="total"
                @pagination="pageList"
                />
            </el-card>
            </el-col>
        </el-row>

        <template #footer>
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="confirm">确定</el-button>
        </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { VxeTableInstance } from 'vxe-table';
import useDialog from '@/hooks/useDialog';
import useAutoSearch from '@/hooks/useAutoSearch';
import { CssrcSoftwareCategoryTreeVO } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/types';
import { listCssrcSoftwareInfo } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo';
import { CssrcSoftwareInfoForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo/types';
import { categoryTreeSelect } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/index';

interface PropType {
    modelValue?: CssrcSoftwareInfoForm[] | CssrcSoftwareInfoForm | undefined;
    multiple?: boolean;
    data?: string | number | (string | number)[] | undefined;
    softwareIds?: string | number | (string | number)[] | undefined;
}

const prop = withDefaults(defineProps<PropType>(), {
    multiple: true,
    modelValue: undefined,
    data: undefined,
    softwareIds: undefined
});

const emit = defineEmits(['update:modelValue', 'confirmCallBack']);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const softwareList = ref<CssrcSoftwareInfoForm[]>();
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const categoryName = ref('');
const categoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);
const enabledCategoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);
const selectSoftwareList = ref<CssrcSoftwareInfoForm[]>([]);
const openParams = ref({});

const categoryTreeRef = ref<ElTreeInstance>();
const queryFormRef = ref<ElFormInstance>();
const tableRef = ref<VxeTableInstance<CssrcSoftwareInfoForm>>();

const softwareDialog = useDialog({
    title: '软件选择'
});
const { sys_file_secret } = toRefs<any>(proxy?.useDict('sys_file_secret'));

const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    softwareName: '',
    manufacturer: '',
    categoryId: '',
    softwareIds: ''
});

const defaultSelectSoftwareIds = computed<string[]>(() => computedIds(prop.data));

/** 根据名称筛选分类树 */
watchEffect(
    () => {
        categoryTreeRef.value?.filter(categoryName.value);
    },
    {
        flush: 'post'
    }
);

const confirm = () => {
    emit('update:modelValue', selectSoftwareList.value);
    emit('confirmCallBack', selectSoftwareList.value);
    softwareDialog.closeDialog();
};

const computedIds = (data: string | number | (string | number)[] | undefined): string[] => {
    if (Array.isArray(data)) {
        return data.map(item => String(item));
    } else if (typeof data === 'string') {
        return data.split(',').map(id => id.trim());
    } else if (typeof data === 'number') {
        return [String(data)];
    } else {
        console.warn('<SoftwareSelect> The data type of data should be array or string or number, but I received other');
        return [];
    }
};

/** 通过条件过滤节点 */
const filterNode = (value: string, data: any) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
};

/** 查询分类下拉树结构 */
const getCategoryTree = async () => {
    const res = await categoryTreeSelect();
    categoryOptions.value = res.data; // 树结构
    enabledCategoryOptions.value = filterDisabledCategory(res.data); // 也是树结构
};

/** 过滤禁用的软件分类 */
const filterDisabledCategory = (categoryList: CssrcSoftwareCategoryTreeVO[]): CssrcSoftwareCategoryTreeVO[] => {
    return categoryList.filter((category) => {
        if (category.disabled) {
        return false;
        }
        if (category.children && category.children.length) {
        category.children = filterDisabledCategory(category.children);
        }
        return true;
    });
};

/** 查询软件列表 */
const getList = async () => {
    loading.value = true;
    queryParams.value.softwareIds = String(prop.softwareIds);
    const res = await listCssrcSoftwareInfo(queryParams.value);
    loading.value = false;
    softwareList.value = res.rows;
    total.value = res.total;
};

const pageList = async () => {
    await getList();
    const softwares = softwareList.value.filter((item) => {
        return selectSoftwareList.value.some((software) => software.softwareId === item.softwareId);
    });
    await tableRef.value.setCheckboxRow(softwares, true);
};

/** 节点单击事件 */
const handleNodeClick = (data: CssrcSoftwareCategoryTreeVO) => {
    queryParams.value.categoryId = String(data.id);
    handleQuery();
};

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
};

/** 重置按钮操作 */
const resetQuery = (refresh = true) => {
    queryFormRef.value?.resetFields();
    queryParams.value.pageNum = 1;
    queryParams.value.categoryId = undefined;
    categoryTreeRef.value?.setCurrentKey(undefined);
    refresh && handleQuery();
};

const handleCheckboxChange = (checked: any) => {
    if (!prop.multiple && checked.checked) {
        tableRef.value.setCheckboxRow(selectSoftwareList.value, false);
        selectSoftwareList.value = [];
    }
    const row = checked.row;
    if (checked.checked) {
        selectSoftwareList.value.push(row);
    } else {
        selectSoftwareList.value = selectSoftwareList.value.filter((item) => {
        return item.softwareId !== row.softwareId;
        });
    }
};

const handleCheckboxAll = (checked: any) => {
    const rows = softwareList.value;
    if (checked.checked) {
        rows.forEach((row) => {
        if (!selectSoftwareList.value.some((item) => item.softwareId === row.softwareId)) {
            selectSoftwareList.value.push(row);
        }
        });
    } else {
        selectSoftwareList.value = selectSoftwareList.value.filter((item) => {
        return !rows.some((row) => row.softwareId === item.softwareId);
        });
    }
};

const handleCloseTag = (software: CssrcSoftwareInfoForm) => {
    const softwareId = software.softwareId;
    const index = selectSoftwareList.value.findIndex((item) => item.softwareId === softwareId);
    const rows = selectSoftwareList.value[index];
    tableRef.value?.setCheckboxRow(rows, false);
    selectSoftwareList.value.splice(index, 1);
};

const close = () => {
    softwareDialog.closeDialog();
};

watch(
    () => softwareDialog.visible.value,
    async (newValue: boolean) => {
        if (newValue) {
        await getCategoryTree(); // 初始化分类数据
        await getList(); // 初始化列表数据
        } else {
        tableRef.value.clearCheckboxReserve();
        tableRef.value.clearCheckboxRow();
        resetQuery(false);
        selectSoftwareList.value = [];
        }
    }
);

// 自动搜索配置
useAutoSearch({
    watchedFields: ['softwareName', 'manufacturer'],
    paramsRef: queryParams,
    onSearch: handleQuery
});

defineExpose({
    open: (params: any) => {
        openParams.value = params;
        softwareDialog.openDialog();
    },
    close: softwareDialog.closeDialog
});
</script>
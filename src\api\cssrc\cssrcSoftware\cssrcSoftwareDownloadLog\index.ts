import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CssrcSoftwareDownloadLogVO, CssrcSoftwareDownloadLogForm, CssrcSoftwareDownloadLogQuery } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareDownloadLog/types';

/**
 * 查询软件下载日志列表
 * @param query
 * @returns {*}
 */

export const listCssrcSoftwareDownloadLog = (query?: CssrcSoftwareDownloadLogQuery): AxiosPromise<CssrcSoftwareDownloadLogVO[]> => {
  return request({
    url: '/cssrc/cssrcSoftwareDownloadLog/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询软件下载日志详细
 * @param logId
 */
export const getCssrcSoftwareDownloadLog = (logId: string | number): AxiosPromise<CssrcSoftwareDownloadLogVO> => {
  return request({
    url: '/cssrc/cssrcSoftwareDownloadLog/' + logId,
    method: 'get'
  });
};

/**
 * 新增软件下载日志
 * @param data
 */
export const addCssrcSoftwareDownloadLog = (data: CssrcSoftwareDownloadLogForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareDownloadLog',
    method: 'post',
    data: data
  });
};

/**
 * 修改软件下载日志
 * @param data
 */
export const updateCssrcSoftwareDownloadLog = (data: CssrcSoftwareDownloadLogForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareDownloadLog',
    method: 'put',
    data: data
  });
};

/**
 * 删除软件下载日志
 * @param logId
 */
export const delCssrcSoftwareDownloadLog = (logId: string | number | Array<string | number>) => {
  return request({
    url: '/cssrc/cssrcSoftwareDownloadLog/' + logId,
    method: 'delete'
  });
};

// 清空操作日志
export function cleanDownloadLog() {
  return request({
    url: '/cssrc/cssrcSoftwareDownloadLog/clean',
    method: 'delete'
  });
}

<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <el-card v-show="showSearch" class="mb-[10px]" shadow="hover">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="公告标题" prop="noticeTitle">
            <el-input v-model="queryParams.noticeTitle" placeholder="请输入公告标题" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="创建人" prop="createByNickName">
            <el-input v-model="queryParams.createByNickName" placeholder="请输入创建人" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="工号" prop="createByName">
            <el-input v-model="queryParams.createByName" placeholder="请输入工号" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="类型" prop="noticeType">
            <el-select v-model="queryParams.noticeType" placeholder="公告类型" clearable>
              <el-option v-for="dict in sys_notice_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="流程状态" prop="flowStatus">
            <el-select v-model="queryParams.flowStatus" placeholder="业务状态" clearable>
              <el-option v-for="dict in wf_business_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:notice:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:notice:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:show-search="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="序号" align="center" prop="noticeId" width="100" />
        <el-table-column label="公告标题" align="center" prop="noticeTitle" :show-overflow-tooltip="true" />
        <el-table-column label="公告类型" align="center" prop="noticeType" width="100">
          <template #default="scope">
            <dict-tag :options="sys_notice_type" :value="scope.row.noticeType" />
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="sys_notice_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建者" align="center" prop="createByNickName" width="150" />
        <el-table-column label="发布时间" align="center" prop="createTime" width="150">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="公告密级" align="center" prop="secret" width="100">
          <template #default="scope">
              <dict-tag :options="sys_file_secret" :value="scope.row.secret" />
          </template>
        </el-table-column>
        <el-table-column label="附件" align="center" width="150">
          <template #default="scope">
            <el-tooltip content="查看附件" placement="top">
              <el-button 
                :disabled="!scope.row.ossIds || scope.row.ossIds.length === 0"
                link
                :type="scope.row.ossIds && scope.row.ossIds.length > 0 ? 'primary' : 'default'"
                icon="Document" @click="handleAttachment(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="center" label="流程状态" width="100">
          <template #default="scope">
            <dict-tag :options="wf_business_status" :value="scope.row.flowStatus"></dict-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top" 
            v-if="scope.row.flowStatus === 'draft' || scope.row.flowStatus === 'cancel' || scope.row.flowStatus === 'back'">
              <el-button v-hasPermi="['system:notice:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top" 
            v-if="scope.row.flowStatus === 'draft' || scope.row.flowStatus === 'cancel' || scope.row.flowStatus === 'back'">
              <el-button v-hasPermi="['system:notice:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="查看" placement="top">
              <el-button type="primary" link icon="View" @click="handleView(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="撤销" placement="top" 
            v-if="scope.row.flowStatus === 'waiting'">
              <el-button type="primary" link icon="Notification" @click="handleCancelProcessApply(scope.row.noticeId)"></el-button>
            </el-tooltip>
            <el-tooltip content="修改显示状态" placement="top" 
              v-if="scope.row.flowStatus === 'finish'">
              <el-button v-hasPermi="['system:notice:edit']" type="primary" link icon="Edit" @click="handleStatus(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="attachmentDialogVisible" title="附件列表" width="40%">
        <el-table :data="attachments">
          <el-table-column type="index" label="序号" align="center" width="60"/>
          <el-table-column label="文件名" align="center"> 
            <template #default="scope">
              <span>{{ scope.row.originalName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="密级" align="center"> 
            <template #default="scope">
              <span>{{ getSecretLabel(scope.row.fileSecret) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleDownload(scope.row)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>

      <el-dialog v-model="dialogVisible" title="修改状态" width="40%">
        <el-form :model="data.form" label-width="80px">
          <el-form-item label="状态">
            <el-radio-group v-model="data.form.status">
              <el-radio v-for="dict in sys_notice_status" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitStatus">确定</el-button>
          </div>
        </template>
      </el-dialog>
      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    
  </div>
</template>

<script setup name="Notice" lang="ts">
import { managementlistNotice, delNotice, updateNotice } from '@/api/management/notice2';
import { NoticeForm, NoticeQuery, NoticeVO } from '@/api/management/notice2/types';
import useAutoSearch from '@/hooks/useAutoSearch'; // 调用自动搜索hook
import { listByIds } from '@/api/system/oss';
import { cancelProcessApply } from '@/api/workflow/instance';
import { useRouter, useRoute } from 'vue-router';
import { useTagsViewStore } from '@/store/modules/tagsView';
import { ref, onMounted, onBeforeUnmount } from 'vue';

const router = useRouter();
const route = useRoute();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_notice_status, sys_notice_type } = toRefs<any>(proxy?.useDict('sys_notice_status', 'sys_notice_type'));
const { wf_business_status } = toRefs<any>(proxy?.useDict('wf_business_status'));

const noticeList = ref<NoticeVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const queryFormRef = ref<ElFormInstance>();

const initFormData: NoticeForm = {
  noticeId: '',
  noticeTitle: '',
  noticeType: '',
  noticeContent: '',
  status: '0',
  remark: '',
  createByName: '',
  createByNickName: '',
  noticeSource: '',
  isTop: '0',
  topDay: undefined,
  downloadCount: 0,
  viewCount: 0,
  flowStatus: '',
  secret: '',
  isRead: undefined,
  ossIds: []
};

const data = reactive<PageData<NoticeForm, NoticeQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    noticeTitle: '',
    createByName: '',
    createByNickName: '',
    status: '',
    noticeType: '',
    flowStatus: '',
    secret: ''
  },
  rules: {
    noticeTitle: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
    noticeType: [{ required: true, message: '公告类型不能为空', trigger: 'change' }],
    noticeContent: [{ required: true, message: "公告内容不能为空", trigger: "blur" }]
  }
});

const { queryParams } = toRefs(data);
const { sys_file_secret } = toRefs<any>(proxy?.useDict('sys_file_secret'));
// 定义临时类型 TempAttachment
interface TempAttachment {
  originalName: string;
  fileUrl: string;
  fileSecret: string;
  ossId: string | number;
}
const attachments = ref<TempAttachment[]>([]);
// 获取密级标签
const getSecretLabel = (fileSecret: string) => {
  const secretType = sys_file_secret.value.find((type: any) => type.value === fileSecret);
  return secretType ? secretType.label : '未知';
};
// 附件下载功能
const handleDownload = async (attachment: TempAttachment) => {
  proxy?.$download.oss(attachment.ossId);
};
/** 查询公告列表 */
const getList = async () => {
  loading.value = true;
  const res = await managementlistNotice(queryParams.value);
  // 添加对<dict-tag>组件value属性的类型校验，确保所有值都不为空
  noticeList.value = res.rows.map(item => ({
    ...item,
    secret: item.secret || '',
    noticeType: item.noticeType || '',
    flowStatus: item.flowStatus || ''
  }));

  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};
/** 多选框选中数据 */
const handleSelectionChange = (selection: NoticeVO[]) => {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};
/** 新增按钮操作 */
const handleAdd = async () => {
  await useTagsViewStore().delView(route);
  proxy.$tab.closePage(route);
  router.push({
    path: `/management/noticeEdit/index`,
    query: {
      type: 'add'
    }
  });
};

/**修改按钮操作 */
const handleUpdate = async (row?: NoticeVO) => {
  proxy.$tab.closePage(route);
  router.push({
    path: `/management/noticeEdit/index`,
    query: {
      noticeId: row.noticeId.toString(),
      type: 'update'
    }
  });
};

/** 查看按钮操作 */
const handleView = (row?: NoticeVO) => {
  proxy.$tab.closePage(route);
  router.push({
    path: `/management/noticeEdit/index`,
    query: {
      noticeId: row.noticeId.toString(),
      type: 'view'
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: NoticeVO) => {
  const noticeIds = row?.noticeId || ids.value;
  await proxy?.$modal.confirm('是否确认删除公告编号为"' + noticeIds + '"的数据项？');
  await delNotice(noticeIds);
  await getList();
  proxy?.$modal.msgSuccess('删除成功');
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/notice/export',
    {
      ...queryParams.value
    },
    `notice_${new Date().getTime()}.xlsx`
  );
};

/** 撤销按钮操作 */
const handleCancelProcessApply = async (noticeId: string) => {
  await proxy?.$modal.confirm('是否确认撤销当前单据？');
  loading.value = true;
  const data = {
    businessId: noticeId,
    message: '申请人撤销流程！'
  };
  await cancelProcessApply(data).finally(() => (loading.value = false));
  await getList();
  proxy?.$modal.msgSuccess('撤销成功');
};

/** 附件查看按钮操作 */
const attachmentDialogVisible = ref(false);
const handleAttachment = async (row?: NoticeVO) => {
  if (row && row.ossIds && row.ossIds.length > 0) {
    try {
      const noticeId = row?.noticeId;
      const selectedNotice = noticeList.value.find(notice => notice.noticeId === noticeId);
      if (selectedNotice && selectedNotice.ossIds && selectedNotice.ossIds.length > 0) {
        try {
          const res = await listByIds(selectedNotice.ossIds.join(',')); // 调用listByIds方法获取ossIds对应的文件详情
          if (Array.isArray(res.data)) {
            attachments.value = res.data.map((file: any) => ({
              originalName: file.originalName,
              fileSecret: file.fileSecret,
              fileUrl: file.url,
              ossId: file.ossId
            }));
          }
        } catch (error) {
          console.error('OSS查询失败，跳过文件信息加载:', error);
        }
      }
    }
    finally {
      attachmentDialogVisible.value = true;
    }
  } 
};

/** 定义已完成修改显示状态 */
const dialogVisible = ref(false);
/** 修改状态操作 */
const handleStatus = async (row?: NoticeVO) => {
  data.form = {
    ...initFormData,
    ...row,
    status: row.status
  };
  dialogVisible.value = true;
};
/** 提交状态修改 */
const submitStatus = async () => {
  try {
    // 调用更新公告状态的API
    await updateNotice(data.form);
    // 关闭对话框并刷新列表
    dialogVisible.value = false;
    proxy?.$modal.msgSuccess("状态修改成功");
    await getList();
  } catch (error) {
    proxy?.$modal.msgError("状态修改失败");
    console.error('状态修改失败:', error);
  }
}
// 自动搜索配置
useAutoSearch({
  watchedFields: ['noticeTitle', 'createByNickName', 'createByName', 'noticeType', 'flowStatus'], // 保持原字段监听
  paramsRef: queryParams, // 主参数对象
  onSearch: handleQuery
});
// 检查依赖项懒加载
onMounted(async () => {
  // 确保所有依赖项加载完毕后再调用 getList
  await nextTick();
  getList();
});
onBeforeUnmount(() => {
  // 清理组件状态
  loading.value = false;
  noticeList.value = [];
});
</script>

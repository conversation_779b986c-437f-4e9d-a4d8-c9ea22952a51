<template>
  <div class="p-2">
    <el-card shadow="never" style="border: 0px;">
        <div style="display: flex; justify-content: space-between">
            <div>
                <el-button v-if="submitButtonShow" :loading="buttonLoading" type="primary" @click="submitForm('submit')">提交</el-button>
                <el-button v-if="approvalButtonShow" :loading="buttonLoading" type="primary" @click="approvalVerifyOpen">审批</el-button>
                <el-button v-if="form && form.id && form.flowStatus !== 'draft'" type="primary" @click="handleApprovalRecord">流程进度</el-button>
            </div>
            <div>
                <el-button style="float: right" @click="goBack()">返回</el-button>
            </div>
        </div>
    </el-card>
    <el-card shadow="never" style="height: 82vh; overflow-y: auto">
        <el-form ref="userFormRef" v-loading="loading" :disabled="routeParams.type === 'view' || routeParams.type === 'approval'" :model="form" :rules="rules" label-width="80px">
            <el-row>
            <el-col :span="10">
                <el-form-item label="用户姓名" prop="nickName">
                    <el-input v-model="form.nickName" placeholder="请输入用户姓名" maxlength="30" />
                </el-form-item>
            </el-col>
            <el-col :span="10">
                <el-form-item label="归属部门" prop="deptId">
                    <el-tree-select
                        v-model="form.deptId"
                        :data="enabledDeptOptions"
                        :props="{ value: 'id', label: 'label', children: 'children' } as any"
                        value-key="id"
                        placeholder="请选择归属部门"
                        check-strictly
                        @change="handleDeptChange"
                    />
                </el-form-item>
            </el-col>
            </el-row>
            <el-row>
            <el-col :span="10">
                <el-form-item v-if="form.userId == undefined" label="用户工号" prop="userName">
                    <el-input v-model="form.userName" placeholder="请输入用户工号" maxlength="30" />
                </el-form-item>
            </el-col>
            <el-col :span="10">
                <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
                    <el-input v-model="form.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password />
                </el-form-item>
            </el-col>
            </el-row>
            <el-row>
            <el-col :span="10">
                <el-form-item label="用户密级" prop="secret">
                <el-select v-model="form.secret" placeholder="请选择用户密级">
                    <el-option
                        v-for="dict in sys_user_secret"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                    ></el-option>
                </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="10">
                <el-form-item label="用户性别">
                <el-select v-model="form.sex" placeholder="请选择">
                    <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
                </el-form-item>
            </el-col>
            </el-row>
            <el-row>
            <el-col :span="10">
                <el-form-item label="岗位">
                <el-select v-model="form.postIds" multiple placeholder="请选择">
                    <el-option
                        v-for="item in postOptions"
                        :key="item.postId"
                        :label="item.postName"
                        :value="item.postId"
                        :disabled="item.status == '1'"
                    ></el-option>
                </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="10">
                <el-form-item label="手机号码" prop="phonenumber">
                <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
                </el-form-item>
            </el-col>
            </el-row>
            <el-row>
            <el-col :span="10">
                <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
                </el-form-item>
            </el-col>
            <el-col :span="10">
                <el-form-item label="状态">
                <el-radio-group v-model="form.status">
                    <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                </el-radio-group>
                </el-form-item>
            </el-col>
            </el-row>
            <el-row>
            <el-col :span="10">
                <el-form-item label="角色" >
                    <el-select v-model="form.roleIds" filterable multiple placeholder="请选择">
                        <el-option
                            v-for="item in roleOptions"
                            :key="item.roleId"
                            :label="item.roleName"
                            :value="item.roleId"
                            :disabled="item.status == '1'"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-col> 
            </el-row>
            <el-row>
            <el-col :span="20">
                <el-form-item label="备注">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
                </el-form-item>
            </el-col>
            </el-row>
        </el-form>
    </el-card>
    <!-- 提交组件 -->
    <submitVerify ref="submitVerifyRef" />
    <!-- 审批记录 -->
    <approvalRecord ref="approvalRecordRef" />
  </div>
</template>

<script setup name="Leave" lang="ts">
import { addUser, getUser, updateUser } from '@/api/system/user';
import { UserForm, UserQuery, UserVO } from '@/api/system/user/types';
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import ApprovalButton from '@/components/Process/approvalButton.vue';
import { AxiosResponse } from 'axios';
import api from '@/api/system/user';
import { DeptTreeVO, DeptVO } from '@/api/system/dept/types';
import { RoleVO } from '@/api/system/role/types';
import { PostQuery, PostVO } from '@/api/system/post/types';
import { optionselect } from '@/api/system/post';

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_normal_disable, sys_user_sex, sys_user_secret } = toRefs<any>(proxy?.useDict('sys_normal_disable', 'sys_user_sex', 'sys_user_secret'));
const postOptions = ref<PostVO[]>([]);
const roleOptions = ref<RoleVO[]>([]);
const deptName = ref('');
const deptOptions = ref<DeptTreeVO[]>([]);
const enabledDeptOptions = ref<DeptTreeVO[]>([]);
const initPassword = ref<string>('');

/* 审批组件相关定义 */
const buttonLoading = ref(false);
const loading = ref(true);
const routeParams = ref<Record<string, any>>({}); // 路由参数
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>(); // 提交组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>(); // 审批记录组件
//按钮组件
const approvalButtonRef = ref<InstanceType<typeof ApprovalButton>>();
const userFormRef = ref<ElFormInstance>();

const initFormData: UserForm = {
    id: undefined,
    userId: undefined,
    deptId: undefined,
    userName: '',
    nickName: undefined,
    secret: '5',
    password: '',
    phonenumber: undefined,
    email: undefined,
    sex: undefined,
    status: '0',
    remark: '',
    postIds: [],
    roleIds: [],
    flowStatus: undefined
};
const data = reactive<PageData<UserForm, UserQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: '',
        nickName: '',
        secret: '',
        phonenumber: '',
        status: '',
        deptId: '',
        roleId: '',
        flowStatus: undefined
    },
    rules: {
        userName: [
        { required: true, message: '工号不能为空', trigger: 'blur' },
        {
            min: 2,
            max: 20,
            message: '工号长度必须介于 2 和 20 之间',
            trigger: 'blur'
        }
        ],
        nickName: [{ required: true, message: '用户姓名不能为空', trigger: 'blur' }],
        secret: [
        { required: true, message: "用户密级不能为空", trigger: "change" }
        ],
        password: [
        { required: true, message: '用户密码不能为空', trigger: 'blur' },
        {
            min: 5,
            max: 20,
            message: '用户密码长度必须介于 5 和 20 之间',
            trigger: 'blur'
        },
        { pattern: /^[^<>"'|\\]+$/, message: '不能包含非法字符：< > " \' \\ |', trigger: 'blur' }
        ],
        email: [
        {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change']
        }
        ],
        roleIds: [{ required: true, message: '用户角色不能为空', trigger: 'blur' }],
        phonenumber: [
        {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
        }
        ]
    }
});
const { form, rules } = toRefs(data);

/** 查询部门下拉树结构 */
const getDeptTree = async () => {
  const res = await api.deptTreeSelect();
  deptOptions.value = res.data;
  enabledDeptOptions.value = filterDisabledDept(res.data);
};

/** 过滤禁用的部门 */
const filterDisabledDept = (deptList: DeptTreeVO[]) => {
  return deptList.filter((dept) => {
    if (dept.disabled) {
      return false;
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children);
    }
    return true;
  });
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  userFormRef.value?.resetFields();
};

// 在适当的位置添加获取角色选项的方法
const getRoleOptions = async () => {
  try {
    const { data } = await api.getUser(); // 获取角色列表的方法
    roleOptions.value = data.roles;
    form.value.password = initPassword.value.toString();
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
};
// 更新岗位选项
const updatePostOptions = async (deptId: number | string) => {
  try {
    const response = await optionselect(deptId);
    postOptions.value = response.data;
  } catch (error) {
    console.error('获取岗位列表失败:', error);
    ElMessage.error('获取岗位信息失败');
  }
};
// 部门变更
const handleDeptChange = async (value: number | string) => {
    if (value) {
    await updatePostOptions(value);
    // 清空已选择的岗位
    form.value.postIds = [];
  } else {
    postOptions.value = [];
    form.value.postIds = [];
  }
}

/** 获取详情 */
const getInfo = () => {
  loading.value = true;
  buttonLoading.value = false;
  nextTick(async () => {
    const res = await getUser(routeParams.value.userId || routeParams.value.id);
    console.log(routeParams.value.id);
    if (res.data.user.userId !== undefined) {
        res.data.user.userId = res.data.user.userId.toString();
    }
    Object.assign(form.value, { ...res.data.user, ...res.data });

    // 根据用户部门获取岗位信息
    if (form.value.deptId) {
      try {
        const response = await optionselect(form.value.deptId);
        postOptions.value = response.data;
        // 确保岗位ID正确赋值
        if (res.data.postIds) {
          form.value.postIds = Array.isArray(res.data.postIds) ? res.data.postIds : [res.data.postIds];
        }
      } catch (error) {
        console.error('获取岗位列表失败:', error);
        ElMessage.error('获取岗位信息失败');
      }
    }

    loading.value = false;
    buttonLoading.value = false;
  });
};
/** 提交按钮 */
const submitForm = (flowStatus: string) => {
    if (!form.value) {  // 检查 form 是否为 null 或 undefined
        ElMessage.error('表单数据无效，请刷新页面重新尝试');
        return;
    }
  try {
    userFormRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        buttonLoading.value = true;
        try {
          let res: AxiosResponse<any>;
          if (form.value.userId) {
            // 修改用户 - 后端会自动处理审批流程
            res = await updateUser(form.value);
          } else {
            // 新增用户 - 后端会自动处理审批流程
            res = await addUser(form.value);
          }

          // 后端返回成功消息，直接显示并返回列表页
          if (res.data) {
            proxy?.$modal.msgSuccess(res.data || '操作成功，已提交审批');
            await proxy.$tab.closePage(route);
            router.go(-1);
          } else {
            ElMessage.error('操作失败，请稍后再试');
          }
        } catch (error: any) {
          // 处理后端返回的错误信息
          const errorMessage = error?.response?.data?.msg || error?.message || '操作失败，请稍后再试';
          ElMessage.error(errorMessage);
          console.error('提交失败:', error);
        } finally {
          buttonLoading.value = false;
        }
      }
    });
  } catch (error) {
    ElMessage.error('表单验证失败');
    console.error(error);
    buttonLoading.value = false;
  }
};

//审批记录
const handleApprovalRecord = () => {
  approvalRecordRef.value.init(form.value.userId);
};

//返回
const goBack = () => {
    proxy.$tab.closePage(route);
    router.go(-1);
};
//审批
const approvalVerifyOpen = async () => {
  submitVerifyRef.value.openDialog(routeParams.value.taskId);
};

/** 校验提交按钮是否显示 */
const submitButtonShow = computed(() => {
    return (
        routeParams.value.type === 'add' ||
        (routeParams.value.type === 'update' &&
            form.value.flowStatus &&
            (form.value.flowStatus === 'cancel' || 
             form.value.flowStatus === 'back' ||
             form.value.flowStatus === 'finish' || 
             form.value.flowStatus === 'invalid' ||
             form.value.flowStatus === 'termination')
        )
    );
});
/** 校验审批按钮是否显示 */
const approvalButtonShow = computed(() => {
    return routeParams.value.type === 'approval' && form.value.flowStatus && form.value.flowStatus === 'waiting';
});

onMounted(() => {
  getDeptTree(); // 初始化部门数据
  getRoleOptions(); // 初始化角色数据
  proxy?.getConfigKey('sys.user.initPassword').then((response) => {
    initPassword.value = response.data;
  });
  nextTick(async () => {
    routeParams.value = route.query;
    reset();
    loading.value = false;
    if (routeParams.value.type === 'update' || routeParams.value.type === 'view' || routeParams.value.type === 'approval') {
      getInfo();
    }
  });
});
</script>
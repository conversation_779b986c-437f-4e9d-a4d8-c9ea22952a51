export interface ExpenditurePlanVO {
  /* 数据列表id */
  id: string | number;
  /**
   * 部门id
   */
  deptId: string | number;
  deptName: string;
  /**
   * 用户id
   */
  userId: string | number;
  userName: string;
  nickName: string;
  /**
   * 支出类别（材料费、专用费、外协费、分拨款）
   */
  expenditureType: string;

  /**
   * 内容
   */
  content: string;

  /**
   * 课题编号
   */
  subjectNumber: string;

  /**
   * 课题名称
   */
  subjectName: string;

  /**
   * 项目类别
   */
  projectType: string;

  /**
   * 合同总金额（万元）
   */
  contractTotalAmount: number;

  /**
   * 本次付款金额（万元）
   */
  currentPaymentAmount: number;

  /**
   * 付款时间
   */
  paymentTime: string;

  /**
   * 是否零余额项目（Y表示是，N表示否）
   */
  isNobalanceProject: string;

  /**
   * 是否重点项目（Y表示是，N表示否）
   */
  isMajorProject: string;

  /**
   * 项目是否到款（Y表示是，N表示否）
   */
  isFunded: string;

  /**
   * 支付方式（银行转账、银行承兑）
   */
  paymentMethod: string;

  /**
   * 流程状态
   */
  flowStatus: string;

  /** 排序列 */
  orderColumn: string;

  /** 排序顺序 */
  orderType: string;
}

export interface ExpenditurePlanForm extends BaseEntity {
  /* 数据列表id */
  id: string | number;
  /**
   * 部门id
   */
  deptId: string | number;
  deptName: string;
  /**
   * 用户id
   */
  userId: string | number;
  userName: string;
  nickName: string;
  /**
   * 支出类别（材料费、专用费、外协费、分拨款）
   */
  expenditureType?: string;

  /**
   * 内容
   */
  content?: string;

  /**
   * 课题编号
   */
  subjectNumber?: string;

  /**
   * 课题名称
   */
  subjectName?: string;

  /**
   * 项目类别
   */
  projectType?: string;

  /**
   * 合同总金额（万元）
   */
  contractTotalAmount?: number;

  /**
   * 本次付款金额（万元）
   */
  currentPaymentAmount?: number;

  /**
   * 付款时间
   */
  paymentTime?: string;

  /**
   * 是否零余额项目（Y表示是，N表示否）
   */
  isNobalanceProject?: string;

  /**
   * 是否重点项目（Y表示是，N表示否）
   */
  isMajorProject?: string;

  /**
   * 项目是否到款（Y表示是，N表示否）
   */
  isFunded?: string;

  /**
   * 支付方式（银行转账、银行承兑）
   */
  paymentMethod?: string;
  /**
   * 流程状态
   */
  flowStatus: string;
}

export interface ExpenditurePlanQuery extends PageQuery {

  /**
   * 部门id
   */
  deptName: string;

  /**
   * 用户id
   */
  userId: string;
  userName: string;
  nickName: string;
  /**
   * 支出类别（材料费、专用费、外协费、分拨款）
   */
  expenditureType?: string;

  /**
   * 课题编号
   */
  subjectNumber?: string;

  /**
   * 课题名称
   */
  subjectName?: string;

  /**
   * 项目类别
   */
  projectType?: string;

  /**
   * 付款时间
   */
  paymentTime?: string;

  /**
   * 是否零余额项目（Y表示是，N表示否）
   */
  isNobalanceProject?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  /**
   * 流程状态
   */
  flowStatus: string;

  /** 排序列 */
  orderColumn: string;
  
  /** 排序顺序 */
  orderType: string;
}




<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUploadRef"
      multiple
      :auto-upload="false"
      list-type="picture"
      :on-success="handleUploadSuccess"
      :limit="limit"
      :accept="fileAccept"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :show-file-list="false"
      :file-list="fileList"
      :on-change="handleChange"
      :drag="false"
      :headers="headers"
      :class="{ hide: fileList.length >= limit }"
    >
    <!-- 
    :action="uploadImgUrl" 
    :before-remove="handleDelete"
    :data="uploadData"
    :on-preview="handlePictureCardPreview"
    -->
      <el-button type="primary">选择图片</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
      </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li v-for="(file) in fileList" :key="file.uid" class="el-upload-list__item ele-upload-list__item-content">
        <div class="thumbnail-container">
          <el-image v-if="file.url" 
            style="width: 50px; height: 50px"
            :src="file.url" 
            :max-scale="7"
            :preview-src-list="[file.url]"
            :initial-index="4"
            fit="cover"
            class="thumbnail" />
        </div>
        <div class="el-icon-document">
          <span> {{ getTruncatedFileName(file.name) }} </span> 
        </div>
        <div class="ele-upload-list__item-content-action">
          <div label="文件密级" class="ele-upload-list__item-content-action-item">密级：
            <el-select v-model="file.fileSecret" placeholder="请选择密级" :class="{'is-error': !file.fileSecret}" class="secret-select">
              <el-option v-for="dict in fileSecretOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </div>
          <div class="ele-upload-list__item-content-status">
            <el-progress v-if="file.status === 'uploading'" :percentage="file.progress" :text-inside="false" :stroke-width="7"  />
            <span v-else-if="file.status === 'waiting'">待上传</span>
            <span v-else-if="file.status === 'success'" style="color:var(--vxe-success-color)">上传成功</span>
            <span v-else-if="file.status === 'fail'" style="color:var(--vxe-danger-color)">上传失败</span>
          </div>
          <div style="width: 36px; margin-top: -4px;"><el-button v-if="file.status !== 'success'" type="danger" link @click="handleDelete(file)">删除</el-button></div>
        </div>
      </li>
    </transition-group>

    <!-- 上传按钮 -->
    <el-button type="primary" @click="uploadFiles" :disabled="fileList.length === 0">上传文件</el-button>
  </div>
</template>

<script setup lang="ts">
import { listByIds, delOss } from '@/api/system/oss';
import { OssVO } from '@/api/system/oss/types';
import { propTypes } from '@/utils/propTypes';
import { globalHeaders } from '@/utils/request';
import { compressAccurately } from 'image-conversion';
import { ref, defineExpose } from 'vue';
import axios from 'axios';

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: () => []
  },
  // 图片数量限制
  limit: propTypes.number.def(5),
  // 大小限制(MB)
  fileSize: propTypes.number.def(5),
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: propTypes.array.def(['png', 'jpg', 'jpeg']),
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  // 是否支持压缩，默认否
  compressSupport: {
    type: Boolean,
    default: false
  },
  // 压缩目标大小，单位KB。默认300KB以上文件才压缩，并压缩至300KB以内
  compressTargetSize: propTypes.number.def(300),
  // 父组件传递密级过滤后的字典数据
  fileSecretOptions: {
    type: [Object, Array],
    requird: true
  }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
// const { sys_file_secret } = toRefs<any>(proxy?.useDict('sys_file_secret'));
const emit = defineEmits(['update:modelValue']);
const number = ref(0);
const uploadList = ref<any[]>([]);
// const dialogImageUrl = ref('');
// const dialogVisible = ref(false);

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadImgUrl = ref(baseUrl + '/resource/oss/upload'); // 上传的图片服务器地址
const headers = ref(globalHeaders());

const fileList = ref<any[]>([]);
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));

// 清空文件列表
const clearFileList = () => {
  fileList.value = [];
}
// 判断是否成功上传所有文件
const allFilesUploaded = computed(() => {
  return fileList.value.length > 0 && fileList.value.every(file => file.status === 'success');
});
defineExpose({
  clearFileList,
  allFilesUploaded,
  getFileList:() => fileList.value
})

const isValid = ref(false);
const isAllFileSecretSelected = computed(() => {
  return fileList.value.length > 0 && fileList.value.every(file => file.fileSecret !== undefined && file.fileSecret !== '');
});
watch(isAllFileSecretSelected, (newValue) => {
  isValid.value = newValue;
});

// 监听 fileType 变化，更新 fileAccept
const fileAccept = computed(() => props.fileType.map((type) => `.${type}`).join(','));

watch(
  () => props.modelValue,
  async (val) => {
    // 处理 props.modelValue 的变化
    if (val) {
      // 首先将值转为数组
      let list: OssVO[] = [];
      if (Array.isArray(val)) {
        list = val as OssVO[];
      } else {
        const res = await listByIds(val);
        list = res.data;
      }
      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        // 字符串回显处理 如果此处存的是url可直接回显 如果存的是id需要调用接口查出来
        let itemData;
        if (typeof item === 'string') {
          itemData = { name: item, url: item };
        } else {
          // 此处name使用ossId 防止删除出现重名
          itemData = { name: item.ossId, url: item.url, ossId: item.ossId };
        }
        return itemData;
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

// 选择文件进入等待上传列表
const handleChange = (file: UploadFile) => {
  const isVaildFile = handleBeforeUpload(file.raw);
  console.log('isVaildFile', isVaildFile);
  if (isVaildFile) {
    fileList.value.push({
      file,
      name:file.name,
      url: file.url,
      fileSecret: '',
      uid: new Date().getTime() + fileList.value.length + 1,
      isTemp: true, // 标记为临时文件
      status: 'waiting', // 初始状态：标记为等待上传状态
      progress: 0
    });
  }
};
/** 上传前loading加载 */
const handleBeforeUpload = (file: any) => {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = '';
    if (file.name.lastIndexOf('.') > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
    }
    // 判断文件类型是否在允许的fileType列表中
    isImg = props.fileType.some((type: any) => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf('image') > -1;
  }
  if (!isImg) {
    proxy?.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join('/')}图片格式文件!`);
    return false;
  }
  if (file.name.includes(',')) {
    proxy?.$modal.msgError('文件名不正确，不能包含英文逗号!');
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy?.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  return true;
};

// 文件个数超出
const handleExceed = () => {
  proxy?.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
};

// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
  if (res.code === 200) {
    file.status = 'success';
    proxy?.$modal.msgSuccess('上传成功');
    // uploadList.value.push({ name: res.data.fileName, url: res.data.url, ossId: res.data.ossId });
    // uploadedSuccessfully();
  } else {
    file.status = 'fail';
    proxy?.$modal.msgError(res.msg);
    // number.value--;
    // proxy?.$modal.closeLoading();
    // proxy?.$modal.msgError(res.msg);
    // imageUploadRef.value?.handleRemove(file);
    // uploadedSuccessfully();
  }
};

// 删除图片
const handleDelete = (file: UploadFile) => {
  const findex = fileList.value.map((f) => f.name).indexOf(file.name);
  // if (findex > -1 && uploadList.value.length === number.value) {
  //   let ossId = fileList.value[findex].ossId;
  //   delOss(ossId);
  //   fileList.value.splice(findex, 1);
  //   emit('update:modelValue', listToString(fileList.value));
  //   return false;
  // }
  // 没有ossId，直接从列表中移除
  fileList.value.splice(findex, 1);
  emit('update:modelValue', listToString(fileList.value));
  proxy?.$modal.msgSuccess('删除成功');
};

// 上传结束处理
// const uploadedSuccessfully = () => {
//   if (number.value > 0 && uploadList.value.length === number.value) {
//     fileList.value = fileList.value.filter((f) => f.url !== undefined).concat(uploadList.value);
//     uploadList.value = [];
//     number.value = 0;
//     emit('update:modelValue', listToString(fileList.value));
//     proxy?.$modal.closeLoading();
//   }
// };
// 文件名称过长时处理
const getTruncatedFileName = (name: string): string => {
  const truncatedName = getFileName(name);
  return truncatedName.length > 30 ? `${truncatedName.slice(0, 26)}...${truncatedName.slice(-4)}` : truncatedName;
};
// 上传失败
const handleUploadError = () => {
  proxy?.$modal.msgError('上传图片失败');
  proxy?.$modal.closeLoading();
};


// 获取文件名称
const getFileName = (name: string) => {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf('/') > -1) {
    return name.slice(name.lastIndexOf('/') + 1);
  } else {
    return name;
  }
};

// 对象转成指定字符串分隔
const listToString = (list: any[], separator?: string) => {
  let strs = '';
  separator = separator || ',';
  for (const i in list) {
    if (undefined !== list[i].ossId && list[i].url.indexOf('blob:') !== 0) {
      strs += list[i].ossId + separator;
    }
  }
  return strs != '' ? strs.substring(0, strs.length - 1) : '';
};

const uploadFiles = () => {
  if (!isValid.value) {
    proxy?.$modal.msgError('请先为所有文件选择密级');
    return;
  }
  fileList.value.forEach(async (file) => {
    if (file.status === 'waiting') {
      file.status = 'uploading'; // 更新文件状态为上传中

      const formData = new FormData();
      formData.append('file', file.file.raw);
      formData.append('fileSecret', file.fileSecret);

      // 使用 axios 或其他 HTTP 客户端发送请求
      axios.post(uploadImgUrl.value, formData, {
          headers: {
            ...headers.value,
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            file.progress = percentCompleted; // 更新文件上传进度
          }
      })
      .then((res) => {
        if (res.data.code === 200) {
          file.status = 'success';
          proxy?.$modal.msgSuccess('上传成功');
        } else {
          file.status = 'fail';
          proxy?.$modal.msgError(res.data.msg);
        }
      })
      .catch(() => {
        file.status = 'fail'; // 更新文件状态为上传失败
        proxy?.$modal.msgError('上传文件失败');
      })
    }
  });
}
</script>

<style scoped lang="scss">
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 3;
  margin-bottom: 10px;
  position: relative;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 350px;
  height: 42px;
  font-size: 14px;
}
.ele-upload-list__item-content-action span {
  width: 60px;
  margin-left: 15px;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 2px;
}
.ele-upload-list__item-content-action-item {
  height: 42px;
  color: #606266;
  font-size: 14px;
  margin-top: 2px;
}
.secret-select {
  width: 140px;
}
.is-error { 
  :deep(.el-select__wrapper) {
    box-shadow: 0 0 0 1px var(--el-color-danger-light-3) inset;
    background-color: var(--el-color-danger-light-9);
  }
}
.thumbnail-container {
  display: flex;
  margin-right: 5px;
}
.thumbnail {
  margin: 5px;
  border-radius: 4px;
}
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
.ele-upload-list__item-content-status {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 10px;
  margin-right: 8px;
  width: 90px;
  height: 14px;
}
.ele-upload-list__item-content-status .el-progress--line {
  max-width: 80px;
}
.el-upload-list__item .el-progress {
  position: relative;
  top:0;
}
</style>

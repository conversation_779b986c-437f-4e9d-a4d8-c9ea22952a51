import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StandardVO, StandardForm, StandardQuery } from '@/api/system/standard/types';

/**
 * 查询标准列表
 * @param query
 * @returns {*}
 */

export const listStandard = (query?: StandardQuery): AxiosPromise<StandardVO[]> => {
  return request({
    url: '/system/standard/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询标准详细
 * @param standardId
 */
export const getStandard = (standardId: string | number): AxiosPromise<StandardVO> => {
  return request({
    url: '/system/standard/' + standardId,
    method: 'get'
  });
};

/**
 * 查询多个标准详细
 * @param standardIds
 */
export const selectStandardByIds = (standardIds: (number | string)[]): AxiosPromise<StandardVO[]> => {
  return request({
    url: '/system/standard/selectStandardByIds?standardIds=' + standardIds,
    method: 'get'
  });
};

/**
 * 新增标准
 * @param data
 */
export const addStandard = (data: StandardForm) => {
  return request({
    url: '/system/standard',
    method: 'post',
    data: data
  });
};

/**
 * 修改标准
 * @param data
 */
export const updateStandard = (data: StandardForm) => {
  return request({
    url: '/system/standard',
    method: 'put',
    data: data
  });
};

/**
 * 删除标准
 * @param standardId
 */
export const delStandard = (standardId: string | number | Array<string | number>) => {
  return request({
    url: '/system/standard/' + standardId,
    method: 'delete'
  });
};

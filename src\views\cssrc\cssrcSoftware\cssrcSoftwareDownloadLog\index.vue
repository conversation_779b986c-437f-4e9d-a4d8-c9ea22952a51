<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="软件名称" prop="softwareName">
              <el-input v-model="queryParams.softwareName" placeholder="请输入软件名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="版本名称" prop="versionName">
              <el-input v-model="queryParams.versionName" placeholder="请输入版本名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="下载用户" prop="userId">
              <el-input v-model="queryParams.userId" placeholder="请输入下载用户ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="IP地址" prop="ipAddress">
              <el-input v-model="queryParams.ipAddress" placeholder="请输入用户IP地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="下载时间" prop="downloadTime">
              <el-date-picker clearable
                v-model="queryParams.downloadTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择下载时间"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['cssrc:cssrcSoftwareDownloadLog:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['monitor:operlog:remove']" type="danger" plain icon="WarnTriangleFilled" @click="handleClean">清空</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['cssrc:cssrcSoftwareDownloadLog:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="cssrcSoftwareDownloadLogList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="日志ID" align="center" prop="logId" v-if="true" width="180"/>
        <el-table-column label="软件名称" align="center" prop="softwareName" />
        <el-table-column label="版本名称" align="center" prop="versionName" />
        <el-table-column label="下载用户" align="center" prop="nickName" />
        <el-table-column label="IP地址" align="center" prop="ipAddress" />
        <el-table-column label="下载时间" align="center" prop="downloadTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.downloadTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="下载状态" align="center" prop="status" >
          <template #default="scope">
            <dict-tag :options="sys_common_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareDownloadLog:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column> -->
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="CssrcSoftwareDownloadLog" lang="ts">
import { listCssrcSoftwareDownloadLog, getCssrcSoftwareDownloadLog, delCssrcSoftwareDownloadLog, addCssrcSoftwareDownloadLog, updateCssrcSoftwareDownloadLog, cleanDownloadLog } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareDownloadLog';
import { CssrcSoftwareDownloadLogVO, CssrcSoftwareDownloadLogQuery, CssrcSoftwareDownloadLogForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareDownloadLog/types';
import { faUnderline } from '@fortawesome/free-solid-svg-icons';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_common_status } = toRefs<any>(proxy?.useDict('sys_common_status'));

const cssrcSoftwareDownloadLogList = ref<CssrcSoftwareDownloadLogVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const cssrcSoftwareDownloadLogFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CssrcSoftwareDownloadLogForm = {
  logId: undefined,
  softwareId: undefined,
  softwareName: undefined,
  versionId: undefined,
  versionName: undefined,
  userId: undefined,
  nickName: undefined,
  ipAddress: undefined,
  downloadTime: undefined,
  status: undefined,
}
const data = reactive<PageData<CssrcSoftwareDownloadLogForm, CssrcSoftwareDownloadLogQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    softwareId: undefined,
    softwareName: undefined,
    versionId: undefined,
    versionName: undefined,
    userId: undefined,
    nickName: undefined,
    ipAddress: undefined,
    downloadTime: undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
    logId: [
      { required: true, message: "日志ID不能为空", trigger: "blur" }
    ],
    softwareId: [
      { required: true, message: "软件ID不能为空", trigger: "blur" }
    ],
    versionId: [
      { required: true, message: "版本ID不能为空", trigger: "blur" }
    ],
    userId: [
      { required: true, message: "下载用户ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询软件下载日志列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCssrcSoftwareDownloadLog(queryParams.value);
  cssrcSoftwareDownloadLogList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  cssrcSoftwareDownloadLogFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CssrcSoftwareDownloadLogVO[]) => {
  ids.value = selection.map(item => item.logId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 提交按钮 */
const submitForm = () => {
  cssrcSoftwareDownloadLogFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.logId) {
        await updateCssrcSoftwareDownloadLog(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addCssrcSoftwareDownloadLog(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: CssrcSoftwareDownloadLogVO) => {
  const _logIds = row?.logId || ids.value;
  await proxy?.$modal.confirm('是否确认删除软件下载日志编号为"' + _logIds + '"的数据项？').finally(() => loading.value = false);
  await delCssrcSoftwareDownloadLog(_logIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 清空按钮操作 */
const handleClean = async () => {
  await proxy?.$modal.confirm('是否确认清空所有操作日志数据项?');
  await cleanDownloadLog();
  await getList();
  proxy?.$modal.msgSuccess('清空成功');
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('cssrc/cssrcSoftwareDownloadLog/export', {
    ...queryParams.value
  }, `cssrcSoftwareDownloadLog_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>

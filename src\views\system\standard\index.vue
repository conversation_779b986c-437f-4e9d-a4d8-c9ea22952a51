<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="标准编号" prop="standardCode">
              <el-input v-model="queryParams.standardCode" placeholder="请输入标准编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="标准名称" prop="standardName">
              <el-input v-model="queryParams.standardName" placeholder="请输入标准名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="标准状态" prop="standardStatus">
              <el-select v-model="queryParams.standardStatus" placeholder="请选择标准状态" clearable >
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:standard:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:standard:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:standard:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:standard:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="standardList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="标准id" align="center" prop="standardId" v-if="false" />
        <el-table-column label="标准编号" align="center" prop="standardCode" />
        <el-table-column label="标准名称" align="center" prop="standardName" />
        <el-table-column label="密级" align="center" prop="secret" >
          <template #default="scope">
            <dict-tag :options="sys_file_secret" :value="scope.row.secret"/>
          </template>
        </el-table-column>
        <el-table-column label="标准文件id" align="center" prop="standardFile" v-if="false"/>
        <el-table-column label="标准文件" align="center" prop="standardFileName" >
          <template #default="scope">
            <a :href="scope.row.standardFileUrl" class="standard-file" target="_blank">{{ scope.row.standardFileName }}</a>
          </template>
        </el-table-column>
        <el-table-column label="文件密级" align="center" prop="standardFileSecret">
          <template #default="scope">
            <dict-tag :options="sys_file_secret" :value="scope.row.standardFileSecret || ''" />
          </template>
        </el-table-column>
        <el-table-column label="标准状态" align="center" prop="standardStatus">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.standardStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="发布时间" align="center" prop="updateTime" >
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建部门" align="center" prop="createDeptName" />
        <el-table-column label="创建人" align="center" prop="createByNickName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="上传标准文件" placement="top">
              <el-button link type="primary" icon="Upload" @click="handleUpload(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:standard:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:standard:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改标准管理对话框 -->
    <el-dialog 
      :title="dialog.title" 
      v-model="dialog.visible" 
      width="800px"
      append-to-body
      @close="cancel"
      :close-on-click-modal="false"
      :show-close="false">
      <el-form ref="standardFormRef" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="标准编号" prop="standardCode">
          <el-input v-model="form.standardCode" placeholder="请输入标准编号"/>
        </el-form-item>
        <el-form-item label="标准名称" prop="standardName">
          <el-input v-model="form.standardName" placeholder="请输入标准名称"/>
        </el-form-item>
        <el-form-item label="标准密级" prop="secret">
          <el-select v-model="form.secret" placeholder="请选择标准密级" :disabled="isUpdate">
            <el-option
              v-for="dict in filteredSysSecret"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标准状态" prop="standardStatus">
          <el-radio-group v-model="form.standardStatus">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 上传标准文件对话框 -->
    <el-dialog 
      v-model="dialogFile.visible"
      :title="dialogFile.title"
      width="800px"
      append-to-body
      @close="cancel"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <el-form ref="standardFormRef" :model="form" :rules="rulesFile" label-width="100px">
        <el-form-item label="标准文件" prop="standardFile">
          <fileUpload
            ref="fileUploadRef" 
            @upload-success="handleUploadSuccess"
            @update:modelValue="handleFileChange"
            :initialFiles = "initialFiles"
            :v-model="form.standardFile"
            :fileSecretOptions="filteredSysFileSecret"
            :isEdit = "isEdit"
            :disabled = "readOnly"
            :folderId = "0"
            :multiple = "false"
            :v-model:fileSecret="form.standardFileSecret"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitFile">确 定</el-button>
          <el-button @click="cancelFile">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Standard" lang="ts">
import { listByIds } from '@/api/system/oss';
import FileUpload from '@/components/FileUpload/index.vue';
import { listStandard, getStandard, delStandard, addStandard, updateStandard } from '@/api/system/standard';
import { StandardVO, StandardQuery, StandardForm } from '@/api/system/standard/types';
import useAutoSearch from '@/hooks/useAutoSearch'; // 调用自动搜索hook
import { UserVO } from '@/api/system/user/types';
import { getUserProfile } from '@/api/system/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const standardList = ref<StandardVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const standardFormRef = ref<ElFormInstance>();
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialogFile = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: StandardForm = {
  standardId: undefined,
  standardCode: undefined,
  standardName: undefined,
  secret: undefined,
  standardFile: undefined,
  standardStatus: undefined,
  standardFileName: undefined,
  standardFileSecret: '',
  updateTime: undefined,
  createDept: undefined,
  createDeptName: undefined,
  createByNickName: undefined
}
const data = reactive<PageData<StandardForm, StandardQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    standardCode: undefined,
    standardName: undefined,
    secret: undefined,
    standardFile: undefined,
    standardStatus: undefined,
    standardFileName: undefined,
    standardFileSecret: '',
    updateTime: undefined,
    createDept: undefined,
    createDeptName: undefined,
    createByNickName: undefined,
    params: {
    }
  },
  rules: {
    standardCode: [
      { required: true, message: "标准编号不能为空", trigger: "blur" }
    ],
    secret: [
      { required: true, message: "标准密级不能为空", trigger: "blur" }
    ],
    standardName: [
      { required: true, message: "标准名称不能为空", trigger: "blur" }
    ],
    standardStatus: [
      { required: true, message: "规程状态不能为空", trigger: "change" }
    ]
  },
  rulesFile: {
    standardFile: [
      { required: true, message: "标准文件不能为空", trigger: "blur" }
    ],
  }
});

interface PageData<StandardForm, StandardQuery> {
  form: StandardForm;
  queryParams: StandardQuery;
  rules: any;
  rulesFile: any; // 新增 rulesFile 属性并定义类型
}

const { queryParams, form, rules, rulesFile } = toRefs(data);
const isEdit = ref(false); // 附件是否处于编辑状态
const isUpdate = ref(false); // 初始标准密级判断为可编辑
const initialFiles = ref([]); // 初始化附件列表
const readOnly = ref(false); // 编辑器是否只读
// 过滤 sys_file_secret 字典数据
const { sys_file_secret } = toRefs<any>(proxy?.useDict('sys_file_secret'));
const fileUploadRef = ref<InstanceType<typeof FileUpload>>();

// 用户信息状态
const userInfo = ref<Partial<UserVO>>({});
// 当前选中的标准密级（用于文件上传时的密级过滤）
const currentStandardSecret = ref<string | undefined>(undefined);

const getUser = async() => {
  const res = await getUserProfile();
  userInfo.value = res.data.user;
}

//使用用户密级过滤对应的标准密级
const filteredSysSecret = computed(() => {
  const userSecret = userInfo.value.secret;
  if (!sys_file_secret.value?.length) return [];
  if (userSecret === '35') {
    return sys_file_secret.value.filter(
      (dict: any) => ['0', '5', '10', '33', '34', '35'].includes(dict.value)
    )
  }
  else if (userSecret === '5') {
    return sys_file_secret.value.filter(
      (dict: any) => ['0', '5', '10'].includes(dict.value)
    )
  }
  else {
    return sys_file_secret.value;
  }
})

//** 过滤 稀巴烂的 sys_file_secret 字典数据 */
const filteredSysFileSecret = computed(() => {
  // 优先使用 currentStandardSecret，如果没有则使用 form.value.secret
  const currentLevel = currentStandardSecret.value || form.value.secret;
  console.log('当前密级：', currentLevel);
  console.log('sys_file_secret数据：', sys_file_secret.value);
  if (currentLevel !== undefined && sys_file_secret.value?.length > 0) {
    // 定义不同密级对应的排除规则
    const excludeRules: Record<string, string[]> = {
      '34': ['33'],
      '39': ['33', '38'],
      '38': ['34']
    };
    // 获取当前密级的排除值列表，默认为空数组
    const excludeValues = excludeRules[currentLevel] || [];  
    // 过滤出不包含排除值且值小于等于当前密级的项
    const filtered = sys_file_secret.value.filter((item: any) => 
      !excludeValues.includes(item.value) && parseInt(item.value) <= parseInt(currentLevel)
    );
    console.log('过滤后的文件密级选项：', filtered);
    return filtered;
  } else {
    // 如果 currentLevel 未定义，返回所有密级的 label
    return [...sys_file_secret.value];
  }
});

/** 处理文件上传成功事件 */
const handleUploadSuccess = (fileInfo: { ossId: string; originalName: string; fileAll: any }) => {
  form.value.standardFile = fileInfo.ossId;
  initialFiles.value = [{
    name: fileInfo.originalName,
    url: fileInfo.fileAll.url,
    ossId: fileInfo.ossId,
    uid: fileInfo.ossId,
    fileSecret: fileInfo.fileAll.fileSecret,
    status: 'success',
    progress: 100
  }];
};

/** 查询标准列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listStandard(queryParams.value);
    standardList.value = res.rows;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  // 重置当前标准密级
  currentStandardSecret.value = undefined;
  form.value.standardFile = ''; // 清空附件列表
  initialFiles.value = []; // 清空初始文件列表
  isEdit.value = false; // 设置为编辑状态
  standardFormRef.value?.resetFields();
  // 在新增时点击取消，清空FileUpload组件的文件列表
  if (fileUploadRef.value) {
    fileUploadRef.value.clearFileList();
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: StandardVO[]) => {
  ids.value = selection.map(item => item.standardId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  isUpdate.value = false;
  dialog.visible = true;
  dialog.title = "添加标准";
}

const originalFileId = ref<string | number>(''); // // 在data部分增加原始文件ID存储
/** 修改按钮操作 */
const handleUpdate = async (row?: StandardVO) => {
  // reset();
  const _standardId = row?.standardId || ids.value[0]
  const res = await getStandard(_standardId);

  Object.assign(form.value, {
    ...res.data
  });
  isUpdate.value = true; // 修改时锁定密级字段
  dialog.visible = true;
  dialog.title = "修改标准";
}

/** 上传文件按钮操作 */
const handleUpload = async (row?: StandardVO) => {
  const _standardId = row?.standardId || ids.value[0]
  const res = await getStandard(_standardId);

  // 保存原始文件ID
  originalFileId.value = res.data.standardFile;
  console.log('originalFileId:', originalFileId.value);
  // 确保文件密级正确初始化
  Object.assign(form.value, {
    ...res.data
  });
  // 重要：设置当前标准密级，确保文件密级过滤正确工作
  currentStandardSecret.value = res.data.secret;
  console.log('设置当前标准密级：', currentStandardSecret.value);

  // 处理已有文件
  if (res.data.standardFile) {
    try {
      const fileRes = await listByIds(res.data.standardFile);
      initialFiles.value = fileRes.data.map((file: any) => ({
        name: file.originalName,
        url: file.url,
        ossId: file.ossId,
        uid: new Date().getTime() + initialFiles.value.length + 1,
        fileSecret: file.fileSecret || '',
        status: 'success',
        progress: 100
      }));
    } catch (error) {
      console.error('获取文件信息失败：', error);
      initialFiles.value = [];
    }
  } else {
    initialFiles.value = [];
  }
  isEdit.value = true; // 设置为编辑状态
  dialogFile.visible = true;
  dialogFile.title = "上传标准文件";
}

// 监听标准密级变化，同步更新 currentStandardSecret
watch(() => form.value.secret, (newSecret) => {
  if (newSecret !== undefined) {
    currentStandardSecret.value = newSecret;
    console.log('标准密级变化，更新currentStandardSecret：', newSecret);
  }
}, { immediate: true });

/** 取消按钮 */
const cancel = async () => {
  reset();
  dialog.visible = false;
}

/** 文件上传取消按钮 */
const cancelFile = async () => {
  if(isEdit.value) {
    // 如果是编辑模式且文件已被删除，则清空表单中的文件ID
    if (isEdit.value && originalFileId.value && !form.value.standardFile) {
      // 明确设置为空字符串，表示文件已被删除
      form.value.standardFile = '';
      // 强制修改数据库
      buttonLoading.value = true;
      // 在没有文件的情况下执行更新操作但不允许取消
      try {
        await updateStandard(form.value);
        proxy?.$modal.msgWarning("请上传标准文件");
      } catch (error) {
        proxy?.$modal.msgWarning("请上传标准文件");
      } finally {
        buttonLoading.value = false;
      }
      return; // 阻止后续操作
    }
  } 
  // 重置时也要清空当前标准密级
  currentStandardSecret.value = undefined;
  reset();
  dialogFile.visible = false;
  await getList();
}

/** 处理文件变化事件 */
const handleFileChange = (newValue: string) => {
  form.value.standardFile = newValue;
  // 如果文件被完全删除，清空相关字段
  if (!newValue) {
    form.value.standardFileName = undefined;
    form.value.standardFileSecret = undefined;
    initialFiles.value = [];
  }
};

/** 提交按钮 */
const submitForm = () => {
  standardFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
            
      buttonLoading.value = true;
      if (form.value.standardId) {
        await updateStandard(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addStandard(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 提交文件按钮 */
const submitFile = async () => { 
  standardFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 检查文件是否被删除
      if (isEdit.value && originalFileId.value && !form.value.standardFile) {
        // 文件被删除了，确保传递正确的状态给后端
        form.value.standardFile = ''; // 明确设置为空字符串，表示文件已被删除
      }
      buttonLoading.value = true;
      if (form.value.standardId) {
        await updateStandard(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addStandard(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialogFile.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: StandardVO) => {
  const _standardIds = row?.standardId || ids.value;
  await proxy?.$modal.confirm('是否确认删除标准编号为"' + _standardIds + '"的数据项？').finally(() => loading.value = false);
  await delStandard(_standardIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('system/standard/export', {
    ...queryParams.value
  }, `standard_${new Date().getTime()}.xlsx`)
}

// 自动搜索配置
useAutoSearch({
  watchedFields: ['standardCode', 'standardName', 'standardStatus'], // 保持原字段监听
  paramsRef: queryParams, // 主参数对象
  onSearch: handleQuery,
  // 添加额外的监听项：日期选择器的 v-model
  extraRefs: [
    // { key: 'dateRange', ref: dateRange }
  ]
});

onMounted(() => {
  getList();
  getUser();
});
</script>
<style scoped>
  .longlabel {
    align-items: flex-start;
    box-sizing: border-box;
    color: var(--el-text-color-regular);
    display: inline-flex;
    flex: 0 0 auto;
    font-size: var(--el-form-label-font-size);
    justify-content: flex-end;
    padding: 0 12px 0 0;
  }
.standard-file {
  &:hover,
  &:active {
    color: #409EFF;
    cursor: pointer;
  }
}
</style>

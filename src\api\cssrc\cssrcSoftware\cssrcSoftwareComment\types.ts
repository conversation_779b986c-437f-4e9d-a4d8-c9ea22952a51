export interface CssrcSoftwareCommentVO {
  /**
   * 评论ID
   */
  commentId: string | number;

  /**
   * 版本ID
   */
  versionId: string | number;

  /**
   * 用户ID
   */
  userId: string | number;

  /**
   * 用户工号
   */
  userName?: string;

  /**
   * 用户姓名
   */
  nickName?: string;

  /**
   * 评论内容
   */
  content: string;

  /**
   * 回复哪条评论
   */
  replyTo: number;

  /**
   * 状态（0正常 1屏蔽）
   */
  status: string;

}

export interface CssrcSoftwareCommentForm extends BaseEntity {
  /**
   * 评论ID
   */
  commentId?: string | number;

  /**
   * 版本ID
   */
  versionId?: string | number;

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 评论内容
   */
  content?: string;

  /**
   * 回复哪条评论
   */
  replyTo?: number;

  /**
   * 状态（0正常 1屏蔽）
   */
  status?: string;

}

export interface CssrcSoftwareCommentQuery extends PageQuery {

  /**
   * 版本ID
   */
  versionId?: string | number;

  /**
   * 用户ID
   */
  userId?: string | number;

  /**
   * 评论内容
   */
  content?: string;

  /**
   * 回复哪条评论
   */
  replyTo?: number;

  /**
   * 状态（0正常 1屏蔽）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}




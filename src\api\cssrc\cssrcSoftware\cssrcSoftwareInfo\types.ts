export interface CssrcSoftwareInfoVO {
  /**
   * 软件ID
   */
  softwareId: string | number;

  /**
   * 软件分类ID
   */
  categoryId: string | number;

  /**
   * 软件分类名称
   */
  categoryName: string;

  /**
   * 软件名称
   */
  softwareName: string;

  /**
   * 生产厂商
   */
  manufacturer: string;

  /**
   * 生产国别
   */
  country: string;

  /**
   * 软件简介
   */
  intro: string;

  /**
   * 软件密级
   */
  secret: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 下载次数
   */
  downloadCount: number;

  /**
   * 创建人
   */
  createBy: string;
  createByName: string;
  createByNickName: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 创建人部门
   */
  createDeptId: string;
  createDeptName: string;

}

export interface CssrcSoftwareInfoForm extends BaseEntity {
  /**
   * 软件ID
   */
  softwareId?: string | number;

  /**
   * 软件分类ID
   */
  categoryId?: string | number;

  /**
   * 软件分类名称
   */
  categoryName: string;

  /**
   * 软件名称
   */
  softwareName?: string;

  /**
   * 生产厂商
   */
  manufacturer?: string;

  /**
   * 生产国别
   */
  country?: string;

  /**
   * 软件简介
   */
  intro?: string;

  /**
   * 软件密级
   */
  secret: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 下载次数
   */
  downloadCount?: number;

  createByNickName: string;

}

export interface CssrcSoftwareInfoQuery extends PageQuery {

  /**
   * 软件分类ID
   */
  categoryId?: string | number;

  /**
   * 软件分类名称
   */
  categoryName: string;

  /**
   * 软件名称
   */
  softwareName?: string;

  /**
   * 生产厂商
   */
  manufacturer?: string;

  /**
   * 生产国别
   */
  country?: string;

  /**
   * 软件简介
   */
  intro?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 软件密级
   */
  secret: string;

  /**
   * 下载次数
   */
  downloadCount?: number;

  createByNickName: string;

  /**
   * 日期范围参数
   */
  params?: any;
}




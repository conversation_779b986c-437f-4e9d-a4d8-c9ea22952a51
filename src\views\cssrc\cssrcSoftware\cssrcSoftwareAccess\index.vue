<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="软件名称" prop="softwareName">
                <el-input v-model="queryParams.softwareName" placeholder="请输入软件名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建人" prop="createByNickName">
                <el-input v-model="queryParams.createByNickName" placeholder="请输入创建人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建部门" prop="createDept">
              <el-input v-model="queryParams.createDept" placeholder="请输入创建部门" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker clearable
                v-model="queryParams.createTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择创建时间"
              />
            </el-form-item>
            <el-form-item label="流程状态" prop="flowStatus">
              <el-select v-model="queryParams.flowStatus" placeholder="请选择流程状态" clearable >
                <el-option v-for="dict in wf_business_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['cssrc:cssrcSoftwareAccess:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['cssrc:cssrcSoftwareAccess:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="cssrcSoftwareAccessList" @selection-change="handleSelectionChange" highlight-current-row >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="申请id" align="center" prop="accessId" v-if="false" />
        <el-table-column label="软件名称" align="center" prop="softwareName" />
        <el-table-column label="密级" align="center" prop="secret" >
          <template #default="scope">
            <dict-tag :options="sys_file_secret" :value="scope.row.secret"/>
          </template>
        </el-table-column>
        <el-table-column label="入网类型" align="center" prop="accessType" />
        <el-table-column label="计算机联网类型" align="center" prop="networkType" />
        <el-table-column label="创建人" align="center" prop="createByNickName" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人部门" align="center" prop="createDeptName" />
        <el-table-column label="流程状态" align="center" prop="flowStatus">
          <template #default="scope">
            <dict-tag :options="wf_business_status" :value="scope.row.flowStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top" 
              v-if="scope.row.flowStatus === 'draft' || scope.row.flowStatus === 'cancel' || scope.row.flowStatus === 'back'">
                <el-button v-hasPermi="['system:notice:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
              </el-tooltip>
            <el-tooltip content="删除" placement="top" 
              v-if="scope.row.flowStatus === 'draft' || scope.row.flowStatus === 'cancel' || scope.row.flowStatus === 'back'">
                <el-button v-hasPermi="['system:notice:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="查看" placement="top">
                <el-button type="primary" link icon="View" @click="handleView(scope.row)"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="撤销" placement="top" 
              v-if="scope.row.flowStatus === 'waiting'">
                <el-button type="primary" link icon="Notification" @click="handleCancelProcessApply(scope.row.noticeId)"></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="CssrcSoftwareAccess" lang="ts">
import { listCssrcSoftwareAccess, delCssrcSoftwareAccess, addCssrcSoftwareAccess, updateCssrcSoftwareAccess } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareAccess';
import { CssrcSoftwareAccessVO, CssrcSoftwareAccessQuery, CssrcSoftwareAccessForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareAccess/types';
import { listCssrcSoftwareInfo, delCssrcSoftwareInfo } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo';
import { CssrcSoftwareInfoVO, CssrcSoftwareInfoQuery, CssrcSoftwareInfoForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo/types';
import { CssrcSoftwareCategoryTreeVO } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/types';
import { categoryTreeSelect } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/index';
import { listByIds } from '@/api/system/oss';
import { cancelProcessApply } from '@/api/workflow/instance';
import { useRouter, useRoute } from 'vue-router';
import { useTagsViewStore } from '@/store/modules/tagsView';
import { ref, onMounted, onBeforeUnmount } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_file_secret, wf_business_status } = toRefs<any>(proxy?.useDict('sys_file_secret', 'wf_business_status'));
const router = useRouter();
const route = useRoute();

const cssrcSoftwareAccessList = ref<CssrcSoftwareAccessVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const categoryName = ref('');
const categoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);
const categoryTreeRef = ref<ElTreeInstance>();
const enabledCategoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const cssrcSoftwareAccessFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CssrcSoftwareAccessForm = {
  accessId: undefined,
  accessType: undefined,
  accessResponsible: undefined,
  networkType: undefined,
  computerCode: undefined,
  flowStatus: undefined,
  createDept: undefined,
  createBy: undefined,
  createByNickName: undefined,
  createTime: undefined,
  /** 软件信息 */
  softwareId: undefined,
  categoryId: undefined, // 软件分类ID
  categoryName: undefined,
  softwareName: undefined,
  manufacturer: undefined,
  country: undefined,
  intro: undefined,
  secret: undefined,
  /** 软件版本信息 */
  versionName: undefined,
  platform: undefined,
  architecture: undefined,
  packageType: undefined,
  bits: undefined,
  fileSize: undefined
}
const data = reactive<PageData<CssrcSoftwareAccessForm, CssrcSoftwareAccessQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    accessId: undefined,
    accessType: undefined,
    accessResponsible: undefined,
    networkType: undefined,
    computerCode: undefined,
    flowStatus: undefined,
    createDept: undefined,
    createBy: undefined,
    createByNickName: undefined,
    createTime: undefined,
    /** 软件信息 */
    softwareId: undefined,
    categoryId: undefined, // 软件分类ID
    categoryName: undefined,
    softwareName: undefined,
    manufacturer: undefined,
    country: undefined,
    intro: undefined,
    secret: undefined,
    /** 软件版本信息 */
    versionName: undefined,
    platform: undefined,
    architecture: undefined,
    packageType: undefined,
    bits: undefined,
    fileSize: undefined,
    params: {
    }
  },
  rules: {
    accessType: [
        { required: true, message: "入网类型不能为空", trigger: "blur" }
    ],
    accessResponsible: [
        { required: true, message: "责任人不能为空", trigger: "blur" }
    ],
    networkType: [
        { required: true, message: "计算机联网类型不能为空", trigger: "blur" }
    ],
    softwareName: [
        { required: true, message: "软件名称不能为空", trigger: "blur" }
    ],
    categoryName: [
        { required: true, message: "软件分类不能为空", trigger: "blur" }
    ],
    secret: [
        { required: true, message: "软件密级不能为空", trigger: "blur" }
    ],
    versionName: [
        { required: true, message: "软件版本号不能为空", trigger: "blur" }
    ],
    platform: [
        { required: true, message: "使用平台不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询软件入网申请列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCssrcSoftwareAccess(queryParams.value);
  cssrcSoftwareAccessList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  cssrcSoftwareAccessFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CssrcSoftwareAccessVO[]) => {
  ids.value = selection.map(item => item.accessId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  proxy.$tab.closePage(route);
  router.push({
    path: `/cssrc/cssrcSoftware/cssrcSoftwareAccess/SoftwareFlow/index`,
    query: {
      type: 'add'
    }
  });
}

/** 修改按钮操作 */
const handleUpdate = async (row?: CssrcSoftwareAccessVO) => {
  proxy.$tab.closePage(route);
  router.push({
    path: `/cssrc/cssrcSoftware/cssrcSoftwareAccess/SoftwareFlow/index`,
    query: {
      accessId: row.accessId.toString(),
      type: 'update'
    }
  });
}

/** 查看按钮操作 */
const handleView = (row?: CssrcSoftwareAccessVO) => {
  proxy.$tab.closePage(route);
  router.push({
    path: `/cssrc/cssrcSoftware/cssrcSoftwareAccess/SoftwareFlow/index`,
    query: {
      accessId: row.accessId.toString(),
      type: 'view'
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CssrcSoftwareAccessVO) => {
  const _accessIds = row?.accessId || ids.value;
  await proxy?.$modal.confirm('是否确认删除软件入网申请编号为"' + _accessIds + '"的数据项？').finally(() => loading.value = false);
  await delCssrcSoftwareAccess(_accessIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('cssrc/cssrcSoftwareAccess/export', {
    ...queryParams.value
  }, `cssrcSoftwareAccess_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>

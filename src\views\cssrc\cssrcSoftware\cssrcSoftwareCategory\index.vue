<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="分类名称" prop="categoryName">
              <el-input v-model="queryParams.categoryName" placeholder="请输入分类名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属范围" prop="softwareRange">
              <el-input v-model="queryParams.softwareRange" placeholder="请输入所属范围" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd()" v-hasPermi="['cssrc:cssrcSoftwareCategory:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table
        ref="cssrcSoftwareCategoryTableRef"
        v-loading="loading"
        :data="cssrcSoftwareCategoryList"
        row-key="categoryId"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="分类名称" align="left" prop="categoryName" />
        <!-- <el-table-column label="父级分类ID" align="center" prop="parentId" />
        <el-table-column label="祖级分类路径" align="center" prop="ancestors" />
        <el-table-column label="所属范围" align="center" prop="softwareRange" /> -->
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareCategory:edit']" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top">
              <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareCategory:add']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareCategory:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加或修改软件分类对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="cssrcSoftwareCategoryFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="上级分类名称" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="cssrcSoftwareCategoryOptions"
            :props="{ value: 'categoryId', label: 'categoryName', children: 'children' }"
            value-key="categoryId"
            placeholder="请选择上级分类"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        
        <!-- <el-form-item label="祖级分类路径" prop="ancestors">
          <el-input v-model="form.ancestors" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="所属范围" prop="softwareRange">
          <el-input v-model="form.softwareRange" placeholder="请输入所属范围" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CssrcSoftwareCategory" lang="ts">
import { listCssrcSoftwareCategory, getCssrcSoftwareCategory, delCssrcSoftwareCategory, addCssrcSoftwareCategory, updateCssrcSoftwareCategory } from "@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory";
import { CssrcSoftwareCategoryVO, CssrcSoftwareCategoryQuery, CssrcSoftwareCategoryForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/types';

type CssrcSoftwareCategoryOption = {
  categoryId: number;
  categoryName: string;
  children?: CssrcSoftwareCategoryOption[];
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;;
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const cssrcSoftwareCategoryList = ref<CssrcSoftwareCategoryVO[]>([]);
const cssrcSoftwareCategoryOptions = ref<CssrcSoftwareCategoryOption[]>([]);
const buttonLoading = ref(false);
const showSearch = ref(true);
const isExpandAll = ref(true);
const loading = ref(false);

const queryFormRef = ref<ElFormInstance>();
const cssrcSoftwareCategoryFormRef = ref<ElFormInstance>();
const cssrcSoftwareCategoryTableRef = ref<ElTableInstance>()

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
});


const initFormData: CssrcSoftwareCategoryForm = {
    categoryId: undefined,
    categoryName: undefined,
    parentId: undefined,
    ancestors: undefined,
    softwareRange: undefined,
    status: undefined,
    orderNum: undefined
}

const data = reactive<PageData<CssrcSoftwareCategoryForm, CssrcSoftwareCategoryQuery>>({
  form: {...initFormData},
  queryParams: {
    categoryName: undefined,
    parentId: undefined,
    ancestors: undefined,
    softwareRange: undefined,
    status: undefined,
    params: {
    }
  },
  rules: {
    categoryId: [
      { required: true, message: "分类ID不能为空", trigger: "blur" }
    ],
    categoryName: [
      { required: true, message: "分类名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询软件分类列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCssrcSoftwareCategory(queryParams.value);
  const data = proxy?.handleTree<CssrcSoftwareCategoryVO>(res.data, "categoryId", "parentId");
  if (data) {
    cssrcSoftwareCategoryList.value = data;
    loading.value = false;
  }
}

/** 查询软件分类下拉树结构 */
const getTreeselect = async () => {
  const res = await listCssrcSoftwareCategory();
  cssrcSoftwareCategoryOptions.value = [];
  const data: CssrcSoftwareCategoryOption = { categoryId: 0, categoryName: '顶级节点', children: [] };
  data.children = proxy?.handleTree<CssrcSoftwareCategoryOption>(res.data, "categoryId", "parentId");
  cssrcSoftwareCategoryOptions.value.push(data);
}

// 取消按钮
const cancel = () => {
  reset();
  dialog.visible = false;
}

// 表单重置
const reset = () => {
  form.value = {...initFormData}
  cssrcSoftwareCategoryFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 新增按钮操作 */
const handleAdd = (row?: CssrcSoftwareCategoryVO) => {
  reset();
  getTreeselect();
  if (row != null && row.categoryId) {
    form.value.parentId = row.categoryId;
  } else {
    form.value.parentId = 0;
  }
  dialog.visible = true;
  dialog.title = "添加软件分类";
}

/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  toggleExpandAll(cssrcSoftwareCategoryList.value, isExpandAll.value)
}

/** 展开/折叠操作 */
const toggleExpandAll = (data: CssrcSoftwareCategoryVO[], status: boolean) => {
  data.forEach((item) => {
    cssrcSoftwareCategoryTableRef.value?.toggleRowExpansion(item, status)
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status)
  })
}

/** 修改按钮操作 */
const handleUpdate = async (row: CssrcSoftwareCategoryVO) => {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.parentId = row.parentId;
  }
  const res = await getCssrcSoftwareCategory(row.categoryId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改软件分类";
}

/** 提交按钮 */
const submitForm = () => {
  cssrcSoftwareCategoryFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.categoryId) {
        await updateCssrcSoftwareCategory(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addCssrcSoftwareCategory(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row: CssrcSoftwareCategoryVO) => {
  await proxy?.$modal.confirm('是否确认删除软件分类编号为"' + row.categoryId + '"的数据项？');
  loading.value = true;
  await delCssrcSoftwareCategory(row.categoryId).finally(() => loading.value = false);
  await getList();
  proxy?.$modal.msgSuccess("删除成功");
}

onMounted(() => {
  getList();
});
</script>

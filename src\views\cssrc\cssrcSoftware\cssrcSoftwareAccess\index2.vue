<template>
<div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
            <el-card shadow="hover">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                <el-form-item label="软件名称" prop="softwareName">
                    <el-input v-model="queryParams.softwareName" placeholder="请输入软件名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="生产厂商" prop="manufacturer">
                    <el-input v-model="queryParams.manufacturer" placeholder="请输入生产厂商" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="创建人" prop="createByNickName">
                    <el-input v-model="queryParams.createByNickName" placeholder="请输入创建人" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="流程状态" prop="flowStatus">
                    <el-select v-model="queryParams.flowStatus" placeholder="业务状态" clearable>
                        <el-option v-for="dict in wf_business_status" :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            </el-card>
        </div>
    </transition>

    <el-card shadow="never">
        <template #header>
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['cssrc:cssrcSoftwareAccess:add']">新增</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['cssrc:cssrcSoftwareAccess:export']">导出</el-button>
                </el-col>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
        </template>

        <el-table
            v-loading="loading"
            :data="cssrcSoftwareInfoList"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            highlight-current-row
        >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="软件名称" align="center" prop="softwareName" />
            <el-table-column label="生产厂商" align="center" prop="manufacturer" />
            <el-table-column label="生产国别" align="center" prop="country" />
            <el-table-column label="软件简介" align="center" prop="intro" v-if="false"/>
            <el-table-column label="密级" align="center" prop="secret" >
            <template #default="scope">
                <dict-tag :options="sys_file_secret" :value="scope.row.secret"/>
            </template>
            </el-table-column>
            <!-- <el-table-column label="下载次数" align="center" prop="downloadCount" /> -->
            <el-table-column label="创建人" align="center" prop="createByNickName" />
            <el-table-column label="创建时间" align="center" prop="createTime" >
            <template #default="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
            </template>
            </el-table-column>
            <el-table-column label="创建人部门" align="center" prop="createDeptName" />
            <el-table-column align="center" label="流程状态" prop="flowStatus" width="100">
                <template #default="scope">
                    <dict-tag :options="wf_business_status" :value="scope.row.flowStatus"></dict-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
                <el-tooltip content="修改" placement="top" 
                v-if="scope.row.flowStatus === 'draft' || scope.row.flowStatus === 'cancel' || scope.row.flowStatus === 'back'">
                    <el-button v-hasPermi="['system:notice:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top" 
                v-if="scope.row.flowStatus === 'draft' || scope.row.flowStatus === 'cancel' || scope.row.flowStatus === 'back'">
                    <el-button v-hasPermi="['system:notice:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip content="查看" placement="top">
                    <el-button type="primary" link icon="View" @click="handleView(scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip content="撤销" placement="top" 
                v-if="scope.row.flowStatus === 'waiting'">
                    <el-button type="primary" link icon="Notification" @click="handleCancelProcessApply(scope.row.noticeId)"></el-button>
                </el-tooltip>
            </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 版本管理区域 -->
    <!-- <el-card shadow="never" class="mt-4" v-if="selectedSoftware">
        <template #header>
            <div class="flex justify-between items-center">
            <div class="flex items-center">
                <el-icon class="mr-2"><Box /></el-icon>
                <span class="text-lg font-semibold">{{ selectedSoftware.softwareName }} - 版本管理</span>
            </div>
            <el-button type="primary" plain icon="Plus" @click="handleAddVersion" v-hasPermi="['cssrc:cssrcSoftwareVersion:add']">
                新增版本
            </el-button>
            </div>
        </template>

        <el-table
            v-loading="versionLoading"
            :data="versionList"
            @selection-change="handleVersionSelectionChange"
        >
            <el-table-column label="版本号" align="center" prop="versionName" />
            <el-table-column label="平台" align="center" prop="platform" />
            <el-table-column label="架构" align="center" prop="architecture" />
            <el-table-column label="包类型" align="center" prop="packageType" />
            <el-table-column label="位数" align="center" prop="bits" />
            <el-table-column label="文件大小" align="center" prop="fileSize">
            <template #default="scope">
                {{ formatFileSize(scope.row.fileSize) }}
            </template>
            </el-table-column>
            <el-table-column label="下载次数" align="center" prop="downloadCount" />
            <el-table-column label="密级" align="center" prop="secret" >
            <template #default="scope">
                <dict-tag :options="sys_file_secret" :value="scope.row.secret"/>
            </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
                <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                {{ scope.row.status === '0' ? '正常' : '停用' }}
                </el-tag>
            </template>
            </el-table-column>
            <el-table-column label="上传人" align="center" prop="createByNickName" />
            <el-table-column label="上传时间" align="center" prop="createTime" >
            <template #default="scope">
                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
            </template>
            </el-table-column>
            <el-table-column label="上传部门" align="center" prop="createDeptName" v-if="false"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
                <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdateVersion(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareVersion:edit']"></el-button>
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleDeleteVersion(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareVersion:remove']"></el-button>
                </el-tooltip>
            </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="versionTotal > 0"
            :total="versionTotal"
            v-model:page="versionQueryParams.pageNum"
            v-model:limit="versionQueryParams.pageSize"
            @pagination="getVersionList"
        />
    </el-card> -->

    <!-- 添加或修改版本对话框 -->
    <!-- <el-dialog :title="versionDialog.title" v-model="versionDialog.visible" width="900px" append-to-body>
        <el-form ref="versionFormRef" :model="versionForm" :rules="versionRules" label-width="100px">
            <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="版本号" prop="versionName">
                <el-input v-model="versionForm.versionName" placeholder="请输入版本号" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="平台" prop="platform">
                <el-select v-model="versionForm.platform" placeholder="请选择平台">
                    <el-option label="Windows" value="Windows" />
                    <el-option label="Linux" value="Linux" />
                    <el-option label="macOS" value="macOS" />
                    <el-option label="Android" value="Android" />
                </el-select>
                </el-form-item>
            </el-col>
            </el-row>
            <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="架构" prop="architecture">
                <el-select v-model="versionForm.architecture" placeholder="请选择架构">
                    <el-option label="x86" value="x86" />
                    <el-option label="x64" value="x64" />
                    <el-option label="arm64" value="arm64" />
                </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="位数" prop="bits">
                <el-select v-model="versionForm.bits" placeholder="请选择位数">
                    <el-option label="32" value="32" />
                    <el-option label="64" value="64" />
                </el-select>
                </el-form-item>
            </el-col>
            </el-row>
            <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="包类型" prop="packageType">
                <el-select v-model="versionForm.packageType" placeholder="请选择包类型" >
                    <el-option label="exe" value="exe" />
                    <el-option label="msi" value="msi" />
                    <el-option label="deb" value="deb" />
                    <el-option label="rpm" value="rpm" />
                    <el-option label="zip" value="zip" />
                    <el-option label="tar.gz" value="tar.gz" />
                </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="密级" prop="secret">
                <el-select v-model="versionForm.secret" :disabled="true">
                    <el-option
                    v-for="dict in filteredSysFileSecret"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                    ></el-option>
                </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="状态" prop="status">
                <el-radio-group v-model="versionForm.status">
                    <el-radio value="0">正常</el-radio>
                    <el-radio value="1">停用</el-radio>
                </el-radio-group>
                </el-form-item>
            </el-col>
        </el-row>
        <el-form-item label="软件文件" prop="ossId">
            <fileUpload
                ref="fileUploadRef"
                @upload-success="handleUploadSuccess"
                @update:modelValue="handleFileChange"
                @file-remove="handleFileRemove"
                @immediate-submit="handleImmediateSubmit"
                :initialFiles="initialFiles"
                :fileSecretOptions="filteredSysFileSecret"
                :config-key="'software'"
                :isEdit="isEditVersion"
                :folderId = "0"
                :multiple = "false"
                :v-model:fileSecret="versionForm.versionFileSecret"
            />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="versionForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
    </el-form>
    <template #footer>
        <div class="dialog-footer">
            <el-button :loading="versionButtonLoading" type="primary" @click="submitVersionForm">确 定</el-button>
            <el-button @click="cancelVersion">取 消</el-button>
        </div>
    </template>
    </el-dialog> -->
    </div>
</template>

<script setup name="CssrcSoftwareInfo" lang="ts">
import { listCssrcSoftwareInfo, getCssrcSoftwareInfo, delCssrcSoftwareInfo, addCssrcSoftwareInfo, updateCssrcSoftwareInfo } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo';
import { CssrcSoftwareInfoVO, CssrcSoftwareInfoQuery, CssrcSoftwareInfoForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo/types';
import { CssrcSoftwareCategoryVO, CssrcSoftwareCategoryTreeVO } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/types';
import { categoryTreeSelect } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/index'
import { listCssrcSoftwareVersion, getCssrcSoftwareVersion, delCssrcSoftwareVersion, addCssrcSoftwareVersion, updateCssrcSoftwareVersion } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareVersion';
import { CssrcSoftwareVersionVO, CssrcSoftwareVersionQuery, CssrcSoftwareVersionForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareVersion/types';
import FileUpload from '@/components/FileUpload/index.vue';
import { Box } from '@element-plus/icons-vue';
import { listByIds } from '@/api/system/oss';
import { cancelProcessApply } from '@/api/workflow/instance';
import { useRouter, useRoute } from 'vue-router';
import { useTagsViewStore } from '@/store/modules/tagsView';
import { ref, onMounted, onBeforeUnmount } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable, sys_file_secret, wf_business_status } = toRefs<any>(proxy?.useDict('sys_normal_disable', 'sys_file_secret', 'wf_business_status'));
const router = useRouter();
const route = useRoute();

const cssrcSoftwareInfoList = ref<CssrcSoftwareInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const categoryName = ref('');
const categoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);
const categoryTreeRef = ref<ElTreeInstance>();
const enabledCategoryOptions = ref<CssrcSoftwareCategoryTreeVO[]>([]);
const queryFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
});

const initFormData: CssrcSoftwareInfoForm = {
    softwareId: undefined,
    categoryId: undefined,
    softwareName: undefined,
    secret: undefined,
    categoryName: undefined,
    manufacturer: undefined,
    country: undefined,
    intro: undefined,
    status: undefined,
    downloadCount: undefined,
    createByNickName: undefined,
    flowStatus: '',
}
const data = reactive<PageData<CssrcSoftwareInfoForm, CssrcSoftwareInfoQuery>>({
    form: {...initFormData},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryId: undefined,
        categoryName: undefined,
        softwareName: undefined,
        manufacturer: undefined,
        country: undefined,
        intro: undefined,
        secret: undefined,
        status: undefined,
        downloadCount: undefined,
        createByNickName: undefined,
        flowStatus: '',
        params: {
        }
    },
    rules: {
        softwareId: [
        { required: true, message: "软件ID不能为空", trigger: "blur" }
        ],
        categoryId: [
        { required: true, message: "软件分类ID不能为空", trigger: "blur" }
        ],
        softwareName: [
        { required: true, message: "软件名称不能为空", trigger: "blur" }
        ],
    }
});

const { queryParams } = toRefs(data);

// 过滤 sys_file_secret 字典数据
const filteredSysFileSecret = computed(() => {
    return sys_file_secret.value.filter((dict: any) => dict.value === '0' || dict.value === '5' || dict.value === '10');
});

// 版本管理相关数据
const selectedSoftware = ref<CssrcSoftwareInfoVO | null>(null);
const versionList = ref<CssrcSoftwareVersionVO[]>([]);
const versionLoading = ref(false);
const versionTotal = ref(0);
const versionIds = ref<Array<string | number>>([]);
const versionButtonLoading = ref(false);
const isEditVersion = ref(false);
const initialFiles = ref([]);
const fileUploadRef = ref();

const versionQueryParams = ref<CssrcSoftwareVersionQuery>({
    pageNum: 1,
    pageSize: 10,
    softwareId: undefined,
    versionName: undefined,
    platform: undefined,
    architecture: undefined,
    bits: undefined,
    ossId: undefined,
    versionFileName: undefined,
    versionFileSecret: undefined,
    versionFileUrl: undefined,
    secret: undefined,
    fileSize: undefined,
    downloadCount: undefined
});

const versionDialog = reactive<DialogOption>({
    visible: false,
    title: ''
});

const initVersionFormData: CssrcSoftwareVersionForm = {
    versionId: undefined,
    softwareId: undefined,
    versionName: '',
    platform: '',
    architecture: '',
    packageType: '',
    bits: '',
    fileSize: undefined,
    secret: undefined,
    versionFileName: undefined,
    versionFileSecret: undefined,
    versionFileUrl: undefined,
    remark: '',
    approvalStatus: '0',
    displayStatus: '0',
    status: '0',
    downloadCount: 0
}
const versionForm = ref<CssrcSoftwareVersionForm>({ ...initVersionFormData });
const versionRules = reactive({
    versionName: [{ required: true, message: "版本号不能为空", trigger: "blur" }],
    platform: [{ required: true, message: "平台不能为空", trigger: "change" }],
    architecture: [{ required: true, message: "架构不能为空", trigger: "change" }],
    // ossId: [{ required: true, message: "软件文件不能为空", trigger: "blur" }],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }]
});

/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选软件分类树 */
watchEffect(
    () => {
        categoryTreeRef.value?.filter(categoryName.value);
    },
    {
        flush: 'post' // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
    }
);

/** 查询软件基本信息列表 */
const getList = async () => {
    loading.value = true;
    const res = await listCssrcSoftwareInfo(queryParams.value);
    cssrcSoftwareInfoList.value = res.rows.map(item => ({
        ...item,
        secret: item.secret || '',
        flowStatus: item.flowStatus || ''
    }));
    total.value = res.total;
    loading.value = false;
}

/** 查询软件分类下拉树结构 */
const getCategoryTree = async () => {
const res = await categoryTreeSelect();
  categoryOptions.value = res.data; // 树结构
  enabledCategoryOptions.value = filterDisabledCategory(res.data); // 也是树结构
};

/** 过滤禁用的软件分类 */
const filterDisabledCategory = (categoryList: CssrcSoftwareCategoryTreeVO[]): CssrcSoftwareCategoryTreeVO[] => {
    return categoryList.filter((category) => {
        if (category.disabled) {
        return false;
        }
        if (category.children && category.children.length) {
        category.children = filterDisabledCategory(category.children);
        }
        return true;
    });
};

/** 节点单击事件 */
const handleNodeClick = (data) => {
    queryParams.value.categoryId = data.id;
    handleQuery();
};

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields();
    categoryTreeRef.value?.setCurrentKey(undefined);
    handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CssrcSoftwareInfoVO[]) => {
    ids.value = selection.map(item => item.softwareId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = async () => {
    await useTagsViewStore().delView(route);
    proxy.$tab.closePage(route);
    router.push({
        path: `/cssrc/cssrcSoftware/cssrcSoftwareAccess/SoftwareFlow/index`,
        query: {
            type: 'add'
        }
    });
}

/** 修改按钮操作 */
const handleUpdate = async (row?: CssrcSoftwareInfoVO) => {
    proxy.$tab.closePage(route);
    router.push({
        path: `/cssrc/cssrcSoftware/cssrcSoftwareAccess/SoftwareFlow/index`,
        query: {
            softwareId: row.softwareId.toString(),
            type: 'update'
        }
    });
}
/** 查看按钮操作 */
const handleView = (row?: CssrcSoftwareInfoVO) => {
    proxy.$tab.closePage(route);
    router.push({
        path:  `/cssrc/cssrcSoftware/cssrcSoftwareAccess/SoftwareFlow/index`,
        query: {
            softwareId: row.softwareId.toString(),
            type: 'view'
        }
    });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CssrcSoftwareInfoVO) => {
    const _softwareIds = row?.softwareId || ids.value;
    await proxy?.$modal.confirm('是否确认删除软件基本信息编号为"' + _softwareIds + '"的数据项？').finally(() => loading.value = false);
    await delCssrcSoftwareInfo(_softwareIds);
    proxy?.$modal.msgSuccess("删除成功");
    await getList();
}

/** 导出按钮操作-需修改为审批的导出 */
const handleExport = () => {
    proxy?.download('cssrc/cssrcSoftwareInfo/export', {
        ...queryParams.value
    }, `cssrcSoftwareInfo_${new Date().getTime()}.xlsx`)
}

/** 撤销按钮操作 */
const handleCancelProcessApply = async (softwareId: string) => {
    await proxy?.$modal.confirm('是否确认撤销当前单据？');
    loading.value = true;
    let data = {
        businessId: softwareId,
        message: '申请人撤销流程！'
    };
    await cancelProcessApply(data).finally(() => (loading.value = false));
    await getList();
    proxy?.$modal.msgSuccess('撤销成功');
};


// 版本管理相关方法
/** 软件行点击事件 */
const handleRowClick = (row: CssrcSoftwareInfoVO) => {
    selectedSoftware.value = row;
    versionQueryParams.value.softwareId = row.softwareId;
    getVersionList();
}

/** 获取版本列表 */
const getVersionList = async () => {
    if (!selectedSoftware.value) return;

    versionLoading.value = true;
    try {
        const response = await listCssrcSoftwareVersion(versionQueryParams.value);
        versionList.value = response.rows;
        versionTotal.value = response.total;
    } catch (error) {
        console.error('获取版本列表失败:', error);
    } finally {
        versionLoading.value = false;
    }
}

/** 版本多选框选中数据 */
const handleVersionSelectionChange = (selection: CssrcSoftwareVersionVO[]) => {
    versionIds.value = selection.map(item => item.versionId);
}

/** 新增版本按钮操作 */
const handleAddVersion = () => {
    if (!selectedSoftware.value) {
        proxy?.$modal.msgError('请先选择一个软件');
        return;
    }
    resetVersionForm();
    versionForm.value.softwareId = selectedSoftware.value.softwareId;
    versionForm.value.secret = selectedSoftware.value.secret;
    versionDialog.visible = true;
    versionDialog.title = "新增版本";
    isEditVersion.value = false;
}
const originalFileId = ref<string | number>(''); // // 在data部分增加原始文件ID存储
/** 修改版本按钮操作 */
const handleUpdateVersion = async (row?: CssrcSoftwareVersionVO) => {
    resetVersionForm();
    const versionId = row?.versionId || versionIds.value[0];
    const res = await getCssrcSoftwareVersion(versionId);

    // 保存原始文件ID
    originalFileId.value = res.data.ossId;
    // 确保文件密级正确初始化
    Object.assign(versionForm.value, {
        ...res.data
    });
    if (res.data.ossId) {
        const fileRes = await listByIds(res.data.ossId);
        initialFiles.value = fileRes.data.map((file: any) => ({
        name: file.originalName,
        url: file.url,
        ossId: file.ossId,
        uid: new Date().getTime() + initialFiles.value.length + 1,
        fileSecret: file.fileSecret || '',
        status: 'success',
        progress: 100
        }));
    } else {
        initialFiles.value = [];
    }
    isEditVersion.value = true;
    versionDialog.visible = true;
    versionDialog.title = "修改版本";
}

/** 删除版本按钮操作 */
const handleDeleteVersion = async (row?: CssrcSoftwareVersionVO) => {
    const _versionIds = row?.versionId || versionIds.value;
    await proxy?.$modal.confirm('是否确认删除版本编号为"' + _versionIds + '"的数据项？');
    try {
        await delCssrcSoftwareVersion(_versionIds);
        await getVersionList();
        proxy?.$modal.msgSuccess("删除成功");
    } catch (error) {
        console.error('删除版本失败:', error);
    }
}

/** 提交版本表单 */
const submitVersionForm = async () => {
    const versionFormRef = proxy?.$refs["versionFormRef"] as any;
    if (!versionFormRef) return;

    const valid = await versionFormRef.validate().catch(() => false);
    if (valid) {
        // 检查文件是否被删除
        if (isEditVersion.value &&  !versionForm.value.ossId) {
        // 文件被删除了，确保传递正确的状态给后端
        versionForm.value.ossId = ''; // 明确设置为空字符串，表示文件已被删除
        }
        versionButtonLoading.value = true;
        const originalSecret = versionForm.value.secret;
        console.log('ceshi',originalSecret);
        try {
        if (versionForm.value.versionId != undefined) {
            await updateCssrcSoftwareVersion(versionForm.value);
            proxy?.$modal.msgSuccess("修改成功");
        } else {
            await addCssrcSoftwareVersion(versionForm.value);
            proxy?.$modal.msgSuccess("新增成功");
        }
        versionDialog.visible = false;
        await getVersionList();
        } catch (error) {
        console.error('提交版本表单失败:', error);
        } finally {
        versionButtonLoading.value = false;
        }
    }
}

/** 取消版本按钮 */
const cancelVersion = () => {
    versionDialog.visible = false;
    resetVersionForm();
}

/** 重置版本表单 */
const resetVersionForm = () => {
    versionForm.value = { ...initVersionFormData };
    initialFiles.value = [];
    isEditVersion.value = false;
    if (fileUploadRef.value) {
        fileUploadRef.value.clearFileList();
    }
}

/** 文件上传成功回调 */
const handleUploadSuccess = (fileInfo: { ossId: string; originalName: string; fileAll: any }) => {
    versionForm.value.ossId = fileInfo.ossId;
    versionForm.value.fileSize = fileInfo.fileAll.size;
    // 强制设置文件密级与版本密级一致
    fileInfo.fileAll.fileSecret = versionForm.value.secret;

    initialFiles.value = [{
        name: fileInfo.originalName,
        url: fileInfo.fileAll.url,
        ossId: fileInfo.ossId,
        uid: fileInfo.ossId,
        fileSecret: fileInfo.fileAll.fileSecret,
        status: 'success',
        progress: 100
    }];
}

/** 文件变化回调 */
const handleFileChange = (newValue: string) => {
    versionForm.value.ossId = newValue;
    // 如果文件被完全删除，清空相关字段
    if (!newValue) {
        versionForm.value.versionFileName = undefined;
        versionForm.value.versionFileSecret = undefined;
        initialFiles.value = [];
    }
}

/** 处理文件删除事件 */
const handleFileRemove = async (removedFile: any) => {
  // 从 initialFiles 中移除对应文件
    const index = initialFiles.value.findIndex(file => file.ossId === removedFile.ossId);
    if (index > -1) {
        initialFiles.value.splice(index, 1);
    }

    // 清空版本表单中的文件相关字段
    if (removedFile.ossId) {
        versionForm.value.ossId = '';
        versionForm.value.versionFileName = undefined;
        versionForm.value.versionFileSecret = undefined;
    }
};

/** 处理立即提交事件 */
const handleImmediateSubmit = async () => {
    // 如果是编辑模式且有versionId，立即提交到后端
    console.log('原始文件id',versionForm.value.versionId);
    if (isEditVersion.value && versionForm.value.versionId) {
        try {
        await updateCssrcSoftwareVersion(versionForm.value);
        // proxy?.$modal.msgSuccess("文件上传成功");
        } catch (error) {
        console.error('立即提交失败:', error);
        proxy?.$modal.msgError("文件删除失败");
        }
    }
};

/** 格式化文件大小 */
const formatFileSize = (bytes: number): string => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

onMounted(async () => {
    await nextTick(); // 确保所有依赖项加载完毕后再调用 getList
    getCategoryTree();
    getList();
});
onBeforeUnmount(() => {
    // 清理组件状态
    loading.value = false;
    cssrcSoftwareInfoList.value = [];
});
</script>

<style scoped lang="scss">
.mt-4 {
    margin-top: 1rem;
}

.flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.items-center {
    align-items: center;
}

.mr-2 {
    margin-right: 0.5rem;
}

.text-lg {
    font-size: 1.125rem;
}

.font-semibold {
    font-weight: 600;
}

// 版本管理区域样式
.version-management {
    .el-card__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px 8px 0 0;

        .el-icon {
        color: white;
        }
    }

    .el-table {
        .el-tag {
        font-weight: 500;
        }
    }
}

// 对话框样式优化
.el-dialog {
    .el-form {
        .el-form-item {
        margin-bottom: 18px;
        }
    }
}

// 文件上传区域样式
.upload-section {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    background-color: #fafafa;
    transition: border-color 0.3s;

    &:hover {
        border-color: #409eff;
    }
}
</style>

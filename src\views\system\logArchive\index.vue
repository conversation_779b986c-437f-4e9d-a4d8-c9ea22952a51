<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <el-card shadow="hover">
                <template #header>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="1.5">
                            <el-button v-hasPermi="['system:logArchive:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleEditStart">修改日志转存配置</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button v-hasPermi="['system:logArchive:execute']" type="primary" plain icon="Plus" @click="handleExecute()">手动执行日志转存</el-button>
                        </el-col>
                        <right-toolbar @query-table="getList"></right-toolbar>
                    </el-row>
                </template>

                <el-table v-loading="loading" :data="logArchiveList" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column label="日志保留月数" align="center" prop="retainMonths" >
                        <template #default="scope">
                            <div v-if="isEditing">
                                <el-select v-model="scope.row.retainMonths" placeholder="请选择" @change="handleChange(scope.row)">
                                    <el-option value="12" label="12"/>
                                    <el-option value="36" label="36"/>
                                </el-select>
                            </div>
                            <div v-else>
                                {{ scope.row.retainMonths }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="转存路径" align="center" prop="archivePath" >
                        <template #default="scope">
                            <div v-if="isEditing">
                                <el-input v-model="scope.row.archivePath" placeholder="请输入转存路径" @keyup.enter.native="handleChange(scope.row)"/>
                            </div>
                            <div v-else>
                                {{ scope.row.archivePath }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="自动转存" align="center">
                        <template #default="scope">
                            <el-switch
                                v-model="scope.row.autoArchive"
                                active-value="1"
                                inactive-value="0"
                                @change="handleChangeAutoArchive(scope.row)"
                            ></el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column label="最近转存时间" align="center" prop="lastArchiveTime" :disabled="true" >
                        <template #default="scope">
                            <span>{{ scope.row.lastArchiveTime }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" v-if="isEditing">
                        <template #default="scope">
                            <el-button type="text" @click="handleChange(scope.row)">保存</el-button>
                            <el-button type="text" @click="cancel">取消</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </transition>
    </div>
</template>
<script setup name="LogArchive" lang="ts">
    import { listLogArchive, updateLogArchive, executeLogArchive } from '@/api/system/logArchive';
    import { LogArchiveVO, LogArchiveQuery, LogArchiveForm } from '@/api/system/logArchive/types';
    import { ref, reactive, watch, onMounted } from 'vue';
    import { parseTime } from '@/utils/ruoyi';

    const { proxy } = getCurrentInstance() as ComponentInternalInstance;
    const logArchiveList = ref<LogArchiveVO[]>([]);
    const loading = ref(true);
    const single = ref(true);
    const ids = ref<Array<string | number>>([]);
    const isEditing = ref(false); // 控制编辑状态
    const logArchiveFormRef = ref<ElFormInstance>();

    const initFormData: LogArchiveForm = {
        configId: undefined,
        tenantId: undefined,
        retainMonths: undefined,
        archivePath: undefined,
        autoArchive: undefined,
        createBy: undefined,
        createDept: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        lastArchiveTime: undefined
    };
    const data = reactive<PageData<LogArchiveForm, LogArchiveQuery>>({
        form: { ...initFormData },
        queryParams: {
            pageNum: 1, 
            pageSize: 10,
            retainMonths: undefined,
            archivePath: undefined,
            autoArchive: undefined,
            lastArchiveTime: undefined
        },
        rules: {}
    });
    const { queryParams, form } = toRefs(data);

    /** 查询测试单列表 */
    const getList = async () => {
        loading.value = true;
        const res = await listLogArchive(queryParams.value);
        logArchiveList.value = res.rows.map((item: any) => ({
            ...item
        }));
        loading.value = false;
    };

    /** 多选框选中数据 */
    const handleSelectionChange = (selection: LogArchiveVO[]) => {
        ids.value = selection.map((item) => item.configId);
        single.value = selection.length != 1;
    };

    /** 启动编辑模式 */
    const handleEditStart = () => {
        isEditing.value = true;
    };
    /** 修改按钮操作 */
    const handleChange = async (row: LogArchiveVO) => {
        try {
            await updateLogArchive(row);
            proxy?.$modal.msgSuccess('保存成功');
            isEditing.value = false; // 保存后退出编辑模式
        } catch (error) {
            proxy?.$modal.msgError('保存失败，请重试');
        }
    };
    /** 执行按钮操作 */
    const handleExecute = async () => {
        await executeLogArchive();
    };

    /** 自动转存状态修改 */
    const handleChangeAutoArchive = async (row: LogArchiveVO) => {
        const text = row.autoArchive === '1' ? '启用' : '禁用';
        try {
            // 可选：添加确认弹窗
            await proxy?.$modal.confirm(`确认要"${text}"该配置吗？`);
            await updateLogArchive(row);
            proxy?.$modal.msgSuccess(`${text}成功`);
        } catch (error) {
            proxy?.$modal.msgError(`${text}失败，请重试`);
            // 回滚状态
            row.autoArchive = row.autoArchive === '1' ? '0' : '1';
        }
    };

    /** 取消按钮 */
    const cancel = () => {
        isEditing.value = false;
        ids.value = [];
    };

    onMounted(() => {
        getList();
    });
</script>
<style scoped>
</style>
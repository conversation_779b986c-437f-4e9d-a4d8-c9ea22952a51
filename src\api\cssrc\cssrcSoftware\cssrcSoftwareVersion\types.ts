export interface CssrcSoftwareVersionVO {
  /**
   * 版本ID
   */
  versionId: string | number;

  /**
   * 软件ID
   */
  softwareId: string | number;

  /**
   * 版本号（如 v1.0.0）
   */
  versionName: string;

  /**
   * 平台（windows/linux）
   */
  platform: string;

  /**
   * 架构（x86/x64/arm64）
   */
  architecture: string;

  /**
   * 包类型（exe/msi/deb/rpm/tar.gz）
   */
  packageType: string;

  /**
   * 软件(版本)密级
   */
  secret: string;

  /**
   * 位数（32/64）
   */
  bits: string;

  /**
   * 文件id
   */
  ossId: string;
  versionFileName: string;
  versionFileSecret: string;
  versionFileUrl: string;

  /**
   * 文件大小(字节)
   */
  fileSize: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 审批状态
   */
  approvalStatus: string;

  /**
   * 前台显示状态（0启用 1禁用）
   */
  displayStatus: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 下载次数
   */
  downloadCount: number;

    /**
   * 创建人
   */
  createBy: string;
  createByName: string;
  createByNickName: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 创建人部门
   */
  createDeptId: string;
  createDeptName: string;

}

export interface CssrcSoftwareVersionForm extends BaseEntity {
  /**
   * 版本ID
   */
  versionId?: string | number;

  /**
   * 软件ID
   */
  softwareId?: string | number;

  /**
   * 版本号（如 v1.0.0）
   */
  versionName?: string;

  /**
   * 平台（windows/linux）
   */
  platform?: string;

  /**
   * 架构（x86/x64/arm64）
   */
  architecture?: string;

  /**
   * 包类型（exe/msi/deb/rpm/tar.gz）
   */
  packageType?: string;

    /**
   * 软件(版本)密级
   */
  secret: string;

  /**
   * 位数（32/64）
   */
  bits?: string;

  /**
   * 文件id
   */
  ossId?: string;
  versionFileName: string;
  versionFileSecret: string;
  versionFileUrl: string;

  /**
   * 文件大小(字节)
   */
  fileSize?: number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 审批状态
   */
  approvalStatus?: string;

  /**
   * 前台显示状态（0启用 1禁用）
   */
  displayStatus?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 下载次数
   */
  downloadCount?: number;

}

export interface CssrcSoftwareVersionQuery extends PageQuery {

  /**
   * 软件ID
   */
  softwareId?: string | number;

  /**
   * 版本号（如 v1.0.0）
   */
  versionName?: string;

  /**
   * 平台（windows/linux）
   */
  platform?: string;

  /**
   * 架构（x86/x64/arm64）
   */
  architecture?: string;

  /**
   * 包类型（exe/msi/deb/rpm/tar.gz）
   */
  packageType?: string;

    /**
   * 软件(版本)密级
   */
  secret: string;

  /**
   * 位数（32/64）
   */
  bits?: string;

  /**
   * 文件id
   */
  ossId?: string;
  versionFileName: string;
  versionFileSecret: string;
  versionFileUrl: string;

  /**
   * 文件大小(字节)
   */
  fileSize?: number;

  /**
   * 审批状态
   */
  approvalStatus?: string;

  /**
   * 前台显示状态（0启用 1禁用）
   */
  displayStatus?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 下载次数
   */
  downloadCount?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}




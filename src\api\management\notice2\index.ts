import request from '@/utils/request';
import { NoticeForm, NoticeQuery, NoticeVO } from './types';
import { AxiosPromise } from 'axios';
// 查询公告列表 从函数声明改为函数赋值，便于导出和作为参数传递
export const listNotice = (query: NoticeQuery): AxiosPromise<NoticeVO[]> => {
  return request({
    url: '/system/notice/list', // 请求url是后端api的路径
    method: 'get',
    params: query // 前端传入的请求参数，包含分页参数、筛选条件等
  });
}

// 带权限的查询公告列表
export const managementlistNotice = (query: NoticeQuery): AxiosPromise<NoticeVO[]> => {
  return request({
    url: '/system/notice/managementList',
    method: 'get',
    params: query
  });
}

// 查询公告详细
export const getNotice = (noticeId: string | number): AxiosPromise<NoticeVO> => {
  return request({
    url: '/system/notice/' + noticeId,
    method: 'get'
  });
}

/**
 * 新增公告
 * @param data
 */
export const addNotice = (data: NoticeForm): AxiosPromise<NoticeVO> => {
  return request({
    url: '/system/notice',
    method: 'post',
    data: data
  });
};

// 修改公告
export const updateNotice = (data: NoticeForm): AxiosPromise<NoticeVO> => {
  return request({
    url: '/system/notice',
    method: 'put',
    data: data
  });
}

// 删除公告
export const delNotice = (noticeId: string | number | Array<string | number>) => {
  return request({
    url: '/system/notice/' + noticeId,
    method: 'delete'
  });
}

// 增加附件下载次数
export function increaseNoticeOssDownloadCount(noticeId: string | number, ossId: string | number) {
  return request({
    url: '/system/notice/increaseNoticeOssDownloadCount',
    method: 'put',
    params: {
      noticeId,
      ossId
    }
  });
}

// 删除附件关联表
export function deleteNoticeOssRelation(noticeId: string | number, ossIds: Array<string | number>) {
  return request({
    url: '/system/notice/deleteNoticeOssRelation',
    method: 'put',
    params: {
      noticeId,
      ossIds
    }
  });
}

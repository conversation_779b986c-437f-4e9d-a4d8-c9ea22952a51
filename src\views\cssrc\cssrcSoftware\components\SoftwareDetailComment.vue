<template>
    <div 
        class="comment-item" 
        :class="{ 'comment-reply': level > 0 }"
        :style="{ marginLeft: level * 40 + 'px' }"
    >
        <!-- 评论卡片 -->
        <el-card class="comment-card" shadow="hover">
            <template #header>
                <div class="comment-user-info">
                    <el-avatar 
                        :size="level > 0 ? 32 : 40" 
                        :style="{ backgroundColor: '#409eff' }"
                    >
                    <el-icon :size="level > 0 ? 16 : 20"><User /></el-icon>
                    </el-avatar>
                    <div class="user-details">
                        <div class="comment-author">{{ getUserDisplayName(comment) }}</div>
                        <div class="comment-time">{{ parseTime(comment.createTime) }}</div>
                    </div>
                </div>
            </template>
            
            <div class="comment-body">
                <!-- 评论文本 -->
                <div class="comment-text">
                    <!-- 如果是回复评论，显示回复目标 -->
                    <span class="reply-target" v-if="comment.replyTo && comment.replyTo !== 0">
                        <el-text class="reply-label" type="primary">回复</el-text>
                        <el-text class="reply-user" type="primary">@{{ comment.replyToUserName }}：</el-text>
                    </span>
                    <span class="comment-content-text">{{ comment.content }}</span>
                </div>
                <!-- 评论操作 -->
                <div class="comment-actions">
                    <el-button 
                        :type="comment.userLikeType === 1 ? 'primary' : 'info'" 
                        :plain="comment.userLikeType !== 1"
                        size="small" 
                        @click="handleLike"
                    >
                        <el-icon><Top /></el-icon>赞 ({{ displayLikeCount }})
                    </el-button>
                    <el-button 
                        type="info" 
                        plain 
                        size="small" 
                        @click="handleReply"
                    >
                        <el-icon class="el-icon--left"><ChatDotRound /></el-icon> 回复
                    </el-button>
                </div>
            </div>
        </el-card>

        <!-- 递归渲染子评论 -->
        <div class="comment-replies" v-if="comment.children && comment.children.length > 0">
            <SoftwareDetailComment
                v-for="child in comment.children"
                :key="child.commentId"
                :comment="child"
                :level="level + 1"
                @reply="(childComment) => $emit('reply', childComment)"
                @like="(childComment, likeType) => $emit('like', childComment, likeType)"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { User, Top, ChatDotRound } from '@element-plus/icons-vue'
    import { parseTime } from '@/utils/ruoyi'
    import { ref, computed } from 'vue'

    // 定义 props
    interface Comment {
        commentId: number
        nickName?: string
        userName?: string
        userNickName?: string
        createByName?: string
        userId: number
        replyTo?: number
        replyToUserName?: string
        content: string
        createTime: string
        userLikeType?: number
        likeCount?: number
        children?: Comment[]
    }

    interface Props {
        comment: Comment
        level?: number
    }
    const props = withDefaults(defineProps<Props>(), {
        level: 0
    })

    // 定义 emits
    const emit = defineEmits<{
        (e: 'reply', comment: Comment): void
        (e: 'like', comment: Comment, likeType: number): void
    }>()

    // 用户点赞状态（本地状态）
    const userLikeType = ref(props.comment.userLikeType || 0)

    // 显示的点赞数（考虑本地状态）
    const displayLikeCount = computed(() => {
        const baseCount = props.comment.likeCount || 0
        // 如果用户已点赞且原始数据中未记录该点赞，则加1
        // 如果用户未点赞且原始数据中记录了该点赞，则减1
        if (userLikeType.value === 1 && props.comment.userLikeType !== 1) {
            return baseCount + 1
        } else if (userLikeType.value !== 1 && props.comment.userLikeType === 1) {
            return baseCount - 1
        }
        return baseCount
    })

    // 获取用户显示名称
    const getUserDisplayName = (comment: Comment): string => {
        // 优先显示昵称，如果没有昵称则显示用户名，最后才显示用户ID
        if (comment.nickName && comment.nickName.trim()) {
            return comment.nickName.trim()
        } else if (comment.userName && comment.userName.trim()) {
            return comment.userName.trim()
        } else if (comment.userNickName && comment.userNickName.trim()) {
            return comment.userNickName.trim()
        } else if (comment.createByName && comment.createByName.trim()) {
            return comment.createByName.trim()
        } else {
            return `用户${comment.userId}`
        }
    }

    // 处理回复
    const handleReply = () => {
        emit('reply', props.comment)
    }

    // 处理点赞
    const handleLike = () => {
        // 切换本地点赞状态
        userLikeType.value = userLikeType.value === 1 ? 0 : 1
        // 触发父组件的点赞事件
        emit('like', props.comment, userLikeType.value)
    }
</script>

<style scoped lang="scss">
.comment-item {
    margin-bottom: 16px;
    position: relative;

    &:last-child {
    margin-bottom: 0;
    }

    &.comment-reply {
    margin-left: 20px;
    margin-top: 12px;

    &::before {
        content: '';
        position: absolute;
        left: -20px;
        top: 20px;
        width: 16px;
        height: 2px;
        background-color: #e4e7ed;
    }

    &::after {
        content: '';
        position: absolute;
        left: -20px;
        top: 0;
        width: 2px;
        height: 20px;
        background-color: #e4e7ed;
    }
    }

    .comment-card {
    :deep(.el-card__header) {
        padding: 12px 16px;
    }

    :deep(.el-card__body) {
        padding: 16px;
    }
    }

    .comment-user-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .user-details {
        flex: 1;
        .comment-author {
            font-weight: 600;
            color: #303133;
            font-size: 15px;
            margin-bottom: 2px;
        }
        .comment-time {
            font-size: 12px;
            color: #909399;
        }
    }
    }

    .comment-body {
    .comment-text {
        display: flex;
        flex-direction: row;
        margin-bottom: 12px;

        .reply-target {
            display: flex;
            align-items: center;
            gap: 4px;
            margin: 0 8px 4px 0; 
            padding: 4px 8px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f4ff 100%);
            border-radius: 6px;
            border-left: 3px solid #409eff;

            .reply-label {
                font-size: 12px;
                font-weight: 500;
            }
            .reply-user {
                font-size: 12px;
                font-weight: 600;
            }
        }

        .comment-content-text {
            color: #606266;
            line-height: 1.6;
            font-size: 14px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
    }

    .comment-actions {
        display: flex;
        gap: 4px;
        padding-top: 2px;
        border-top: 1px solid #f5f5f5;
    }
    }

    .comment-replies {
        margin-top: 16px;
        position: relative;
    }
}
</style>
<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="软件ID" prop="softwareId">
              <el-input v-model="queryParams.softwareId" placeholder="请输入软件ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="版本号" prop="versionName">
              <el-input v-model="queryParams.versionName" placeholder="请输入版本号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="平台" prop="platform">
              <el-input v-model="queryParams.platform" placeholder="请输入平台" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="架构" prop="architecture">
              <el-input v-model="queryParams.architecture" placeholder="请输入架构" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="位数" prop="bits">
              <el-input v-model="queryParams.bits" placeholder="请输入位数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文件id" prop="ossId">
              <el-input v-model="queryParams.ossId" placeholder="请输入文件id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文件大小(字节)" prop="fileSize">
              <el-input v-model="queryParams.fileSize" placeholder="请输入文件大小(字节)" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="下载次数" prop="downloadCount">
              <el-input v-model="queryParams.downloadCount" placeholder="请输入下载次数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['cssrc:cssrcSoftwareVersion:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['cssrc:cssrcSoftwareVersion:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['cssrc:cssrcSoftwareVersion:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['cssrc:cssrcSoftwareVersion:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="cssrcSoftwareVersionList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="版本ID" align="center" prop="versionId" v-if="true" />
        <el-table-column label="软件ID" align="center" prop="softwareId" />
        <el-table-column label="版本号" align="center" prop="versionName" />
        <el-table-column label="平台" align="center" prop="platform" />
        <el-table-column label="架构" align="center" prop="architecture" />
        <el-table-column label="包类型" align="center" prop="packageType" />
        <el-table-column label="位数" align="center" prop="bits" />
        <el-table-column label="文件id" align="center" prop="ossId" />
        <el-table-column label="文件大小(字节)" align="center" prop="fileSize" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="审批状态" align="center" prop="approvalStatus" />
        <el-table-column label="前台显示状态" align="center" prop="displayStatus" />
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="下载次数" align="center" prop="downloadCount" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareVersion:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['cssrc:cssrcSoftwareVersion:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改软件版本详情对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="cssrcSoftwareVersionFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="软件ID" prop="softwareId">
          <el-input v-model="form.softwareId" placeholder="请输入软件ID" />
        </el-form-item>
        <el-form-item label="版本号" prop="versionName">
          <el-input v-model="form.versionName" placeholder="请输入版本号" />
        </el-form-item>
        <el-form-item label="平台" prop="platform">
          <el-input v-model="form.platform" placeholder="请输入平台" />
        </el-form-item>
        <el-form-item label="架构" prop="architecture">
          <el-input v-model="form.architecture" placeholder="请输入架构" />
        </el-form-item>
        <el-form-item label="位数" prop="bits">
          <el-input v-model="form.bits" placeholder="请输入位数" />
        </el-form-item>
        <el-form-item label="文件id" prop="ossId">
          <el-input v-model="form.ossId" placeholder="请输入文件id" />
        </el-form-item>
        <el-form-item label="文件大小(字节)" prop="fileSize">
          <el-input v-model="form.fileSize" placeholder="请输入文件大小(字节)" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="下载次数" prop="downloadCount">
          <el-input v-model="form.downloadCount" placeholder="请输入下载次数" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CssrcSoftwareVersion" lang="ts">
import { listCssrcSoftwareVersion, getCssrcSoftwareVersion, delCssrcSoftwareVersion, addCssrcSoftwareVersion, updateCssrcSoftwareVersion } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareVersion';
import { CssrcSoftwareVersionVO, CssrcSoftwareVersionQuery, CssrcSoftwareVersionForm } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareVersion/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const cssrcSoftwareVersionList = ref<CssrcSoftwareVersionVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const cssrcSoftwareVersionFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CssrcSoftwareVersionForm = {
  versionId: undefined,
  softwareId: undefined,
  versionName: undefined,
  platform: undefined,
  architecture: undefined,
  packageType: undefined,
  bits: undefined,
  ossId: undefined,
  fileSize: undefined,
  remark: undefined,
  approvalStatus: undefined,
  displayStatus: undefined,
  status: undefined,
  downloadCount: undefined
}
const data = reactive<PageData<CssrcSoftwareVersionForm, CssrcSoftwareVersionQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    softwareId: undefined,
    versionName: undefined,
    platform: undefined,
    architecture: undefined,
    packageType: undefined,
    bits: undefined,
    ossId: undefined,
    fileSize: undefined,
    approvalStatus: undefined,
    displayStatus: undefined,
    status: undefined,
    downloadCount: undefined,
    params: {
    }
  },
  rules: {
    versionId: [
      { required: true, message: "版本ID不能为空", trigger: "blur" }
    ],
    softwareId: [
      { required: true, message: "软件ID不能为空", trigger: "blur" }
    ],
    versionName: [
      { required: true, message: "版本号不能为空", trigger: "blur" }
    ],
    platform: [
      { required: true, message: "平台不能为空", trigger: "blur" }
    ],
    ossId: [
      { required: true, message: "文件id不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询软件版本详情列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCssrcSoftwareVersion(queryParams.value);
  cssrcSoftwareVersionList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  cssrcSoftwareVersionFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: CssrcSoftwareVersionVO[]) => {
  ids.value = selection.map(item => item.versionId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加软件版本详情";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: CssrcSoftwareVersionVO) => {
  reset();
  const _versionId = row?.versionId || ids.value[0]
  const res = await getCssrcSoftwareVersion(_versionId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改软件版本详情";
}

/** 提交按钮 */
const submitForm = () => {
  cssrcSoftwareVersionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.versionId) {
        await updateCssrcSoftwareVersion(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addCssrcSoftwareVersion(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: CssrcSoftwareVersionVO) => {
  const _versionIds = row?.versionId || ids.value;
  await proxy?.$modal.confirm('是否确认删除软件版本详情编号为"' + _versionIds + '"的数据项？').finally(() => loading.value = false);
  await delCssrcSoftwareVersion(_versionIds);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('cssrc/cssrcSoftwareVersion/export', {
    ...queryParams.value
  }, `cssrcSoftwareVersion_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>

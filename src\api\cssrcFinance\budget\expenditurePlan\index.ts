import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ExpenditurePlanVO, ExpenditurePlanForm, ExpenditurePlanQuery } from '@/api/cssrcFinance/budget/expenditurePlan/types';

/**
 * 查询财务-预算-支出计划列表
 * @param query
 * @returns {*}
 */

export const listExpenditurePlan = (query?: ExpenditurePlanQuery): AxiosPromise<ExpenditurePlanVO[]> => {
  return request({
    url: '/cssrcFinance/budget/expenditurePlan/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询财务-预算-支出计划详细
 * @param id
 */
export const getExpenditurePlan = (id: string | number): AxiosPromise<ExpenditurePlanVO> => {
  return request({
    url: '/cssrcFinance/budget/expenditurePlan/' + id,
    method: 'get'
  });
};

/**
 * 新增财务-预算-支出计划
 * @param data
 */
export const addExpenditurePlan = (data: ExpenditurePlanForm): AxiosPromise<ExpenditurePlanVO> => {
  return request({
    url: '/cssrcFinance/budget/expenditurePlan',
    method: 'post',
    data: data
  });
};

/**
 * 修改财务-预算-支出计划
 * @param data
 */
export const updateExpenditurePlan = (data: ExpenditurePlanForm): AxiosPromise<ExpenditurePlanVO> => {
  return request({
    url: '/cssrcFinance/budget/expenditurePlan',
    method: 'put',
    data: data
  });
};

/**
 * 删除财务-预算-支出计划
 * @param id
 */
export const delExpenditurePlan = (id: string | number | Array<string | number>) => {
  return request({
    url: '/cssrcFinance/budget/expenditurePlan/' + id,
    method: 'delete'
  });
};

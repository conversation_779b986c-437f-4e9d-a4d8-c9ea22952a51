<template>
    <div class="notice-detail-page">
    <el-card class="box-card">
        <div class="notice-header">
            <h2 class="notice-title">{{ noticeInfo.noticeTitle }}</h2>
            <div class="notice-meta">
                <!-- <dict-tag :options="sys_notice_type" :value="noticeInfo.noticeType" /> -->
                <span class="meta-item">
                    <el-icon><User /></el-icon>发布人: {{ noticeInfo.createByName }}
                </span>
                <span class="meta-item">
                    <el-icon><Clock /></el-icon>发布时间: {{ parseTime(noticeInfo.createTime, '{y}-{m}-{d}') }}
                </span>
                <span class="meta-item">
                    <el-icon><View /></el-icon>浏览量: {{ noticeInfo.viewCount }}次
                </span>
            </div>
        </div>
        <el-divider /> <!--创建分隔线-->
        <div class="notice-content" v-html="noticeInfo.noticeContent"></div>
        <!-- 附件 -->
        <div v-if="attachments.length > 0" class="attachments">
            <h4>附件:</h4>
            <ul>
                <li v-for="attachment in attachments" :key="attachment.ossId">
                    <el-button type="text" @click="handleDownload(attachment)" class="download"> 
                        {{ attachment.originalName }}  ({{ getSecretLabel(attachment.fileSecret) }})
                    </el-button>
                </li>
            </ul>
        </div>
    </el-card>
    </div>
</template>

<script setup lang="ts">
    import { ref, onMounted } from 'vue';
    import { useRoute } from 'vue-router'; // useRoute获取路由ID信息
    import { getNotice } from '@/api/management/notice2';
    import { listByIds } from '@/api/system/oss';
    import { NoticeVO } from '@/api/management/notice2/types';
    import { parseTime } from '@/utils/ruoyi';
    import { User, Clock } from '@element-plus/icons-vue';

    const route = useRoute(); // 添加路由route实例
    const noticeInfo = ref<NoticeVO>({} as NoticeVO);
    const { proxy } = getCurrentInstance() as ComponentInternalInstance;
    const { sys_file_secret } = toRefs<any>(proxy?.useDict('sys_file_secret'));

    // 定义临时类型 TempAttachment
    interface TempAttachment {
        originalName: string;
        fileUrl: string;
        fileSecret: string;
        ossId: string | number;
    }
    const attachments = ref<TempAttachment[]>([]);

    const getNoticeDetail = async (noticeId: string | number) => {
        try {
            const { data } = await getNotice(noticeId);
            noticeInfo.value = data;

            // 获取附件详细信息,映射到TempAttachment类型，并赋值给attachments.value
            if (noticeInfo.value.ossIds && noticeInfo.value.ossIds.length > 0) {
                const res = await listByIds(noticeInfo.value.ossIds.join(','));
                attachments.value = res.data.map((file: any) => ({
                    originalName: file.originalName,
                    fileUrl: file.url,
                    fileSecret: file.fileSecret,
                    ossId: file.ossId
                }));
            }
        } catch (error) {
            console.error('获取通知详情失败', error);
        }
    };

    // 获取密级标签
    const getSecretLabel = (fileSecret: string) => {
        const secretType = sys_file_secret.value.find((type: any) => type.value === fileSecret);
        return secretType ? secretType.label : '未知';
    };

    const handleDownload = async (attachment: TempAttachment) => {
        proxy?.$download.oss(attachment.ossId); // 执行下载，预览界面不更新下载次数
    };

    // 当用户即时输入时点击发布预览，获取通知详情
    const props = defineProps({
        noticeInfo: {
            type: Object as PropType<NoticeVO>,
            required: false // 设置为false，表示该属性不是必须的
        },
        attachments: {
            type: Array as PropType<TempAttachment[]>,
            default: () => []
        }
    });
    // 监听noticeInfo变化
    watch(
        () => props.noticeInfo,
        (newVal) => {
            // 只在newVal有值且不为空对象时更新
            if (newVal && Object.keys(newVal).length > 0) {
                console.log('noticeInfo updated:', newVal);
                noticeInfo.value = newVal; // 更新本地数据
            }
        },
        { immediate: true, deep: true } 
        // immediate: true 确保首次加载时也能正确获取数据
        // deep: true 表示监听对象属性的变更，包括数组和对象的属性的变更
    );
    // 监听attachments变化 
    watch(
        () => props.attachments,
        (newVal) => {
            // 只在newVal有值时更新
            if (newVal && newVal.length > 0) {
                console.log('Attachments updated:', newVal);
                attachments.value = newVal; // 更新本地数据
            }
        },
        { immediate: true, deep: true }
    );

    onMounted(() => {
        const noticeId = route.query.noticeId;
        if (noticeId) {
            // 如果 noticeId 是数组，则取第一个元素
            const id = Array.isArray(noticeId) ? noticeId[0] : noticeId;
            // 确保 id 是非空字符串或数字
            if (id && (typeof id === 'string' || typeof id === 'number')) {
                getNoticeDetail(id);
            }
        }
    });
</script>

<style scoped>
    .notice-detail-page {
        padding: 20px;
    }
    .notice-header {
        text-align: center;
        padding: 20px 0 10px 0;
    }
    .notice-title {
        font-size: 1.8em;
        font-weight: bold;
        margin-bottom: 20px;
        color: #303133;
    }
    .notice-meta {
        color: #909399;
        font-size: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20px;
    }
    .meta-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    .notice-content {
        padding: 10px 20px;
        line-height: 1.8;
        font-size: 15px;
        color: #606266;
        white-space: pre-wrap; /* 保留空格和换行 */
    }
    .attachments {
        cursor: default;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: nowrap;
        color: #606266;
    }
    .attachments h4 {
        margin-top:4px;
        margin-bottom: 0;
    }
    .attachments ul {
        padding-left: 10px;
        margin: 0;
        list-style: none;
    }
    .attachments ul li {
        margin-top: 3px;
    }
    .download {
        margin-left: 10px;
        height: 20px;
    }
    /* 为富文本内容添加额外的格式支持 */
    :deep(.notice-content) {
        p {
            margin: 1em 0;
            text-indent: 2em; /* 首行缩进 */
        }
        
        img {
            max-width: 100%;
            height: auto;
        }
        
        ul, ol {
            padding-left: 2em;
            list-style: none;
        }
        
        table {
            border-collapse: collapse;
            margin: 1em 0;
        }
        
        td, th {
            border: 1px solid #ddd;
            padding: 8px;
        }
    }
</style>
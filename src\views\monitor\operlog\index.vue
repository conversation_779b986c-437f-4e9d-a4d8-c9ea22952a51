<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="操作地址" prop="operIp">
              <el-input v-model="queryParams.operIp" placeholder="请输入操作地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="系统模块" prop="title">
              <el-input v-model="queryParams.title" placeholder="请输入系统模块" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工号" prop="operName">
              <el-input v-model="queryParams.operName" placeholder="请输入人员工号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名" prop="operNickName">
              <el-input v-model="queryParams.operNickName" placeholder="请输入人员姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="类型" prop="businessType">
              <el-select v-model="queryParams.businessType" placeholder="操作类型" clearable>
                <el-option v-for="dict in sys_oper_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="操作状态" clearable>
                <el-option v-for="dict in sys_common_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="操作时间" style="width: 308px">
              <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="操作详情" prop="detail">
              <el-input v-model="queryParams.detail" placeholder="请输入操作详情" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="用户密级" prop="secret">
              <el-select v-model="queryParams.secret" placeholder="请选择用户密级" clearable>
                <el-option v-for="dict in sys_user_secret" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['monitor:operlog:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()">
              删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['monitor:operlog:remove']" type="danger" plain icon="WarnTriangleFilled" @click="handleClean">清空</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['monitor:operlog:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:show-search="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        ref="operLogTableRef"
        v-loading="loading"
        :data="operlogList"
        border
        :default-sort="defaultSort"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="日志编号" align="center" prop="operId" />
        <el-table-column label="系统模块" align="center" prop="title" :show-overflow-tooltip="true" />
        <el-table-column label="操作类型" align="center" prop="businessType">
          <template #default="scope">
            <dict-tag :options="sys_oper_type" :value="scope.row.businessType" />
          </template>
        </el-table-column>
        <el-table-column
          label="工号"
          align="center"
          width="110"
          prop="operName"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        />
        <el-table-column
          label="姓名"
          align="center"
          width="110"
          prop="operNickName"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        />
        <el-table-column label="用户密级" align="center" prop="secret">
          <template #default="scope">
            <dict-tag :options="sys_user_secret" :value="scope.row.secret" />
          </template>
        </el-table-column>
        <el-table-column label="部门" align="center" prop="deptName" :show-overflow-tooltip="true" />
        <el-table-column label="IP地址" align="center" prop="operIp" width="130" :show-overflow-tooltip="true" />
        <el-table-column label="操作状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_common_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作日期" align="center" prop="operTime" width="180" sortable="custom" :sort-orders="['descending', 'ascending']">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.operTime) }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="消耗时间"
          align="center"
          prop="costTime"
          width="110"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template #default="scope">
            <span>{{ scope.row.costTime }}毫秒</span>
          </template>
        </el-table-column> -->
        <el-table-column label="操作详情" align="center" prop="detail" width="300" />
        <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="详细" placement="top">
              <el-button v-hasPermi="['monitor:operlog:query']" link type="primary" icon="View" @click="handleView(scope.row)"> </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 操作日志详细 -->
    <OperInfoDialog ref="operInfoDialogRef" />

    <!-- 密级选择对话框 -->
    <el-dialog v-model="secretDialog.visible" title="选择导出文件密级" width="500px" append-to-body>
      <el-form ref="secretFormRef" :model="secretForm" :rules="secretRules" label-width="100px">
        <el-form-item label="文件密级" prop="secret">
          <el-select v-model="secretForm.secret" placeholder="请选择文件密级" style="width: 100%">
            <el-option
              v-for="dict in filteredSysSecret"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSecretForm">确 定</el-button>
          <el-button @click="cancelSecretForm">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Operlog" lang="ts">
import { list, delOperlog, cleanOperlog } from '@/api/monitor/operlog';
import { OperLogForm, OperLogQuery, OperLogVO } from '@/api/monitor/operlog/types';
import OperInfoDialog from './oper-info-dialog.vue';

import { UserVO } from '@/api/system/user/types';
import { getUserProfile } from '@/api/system/user';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_oper_type, sys_common_status, sys_user_secret, sys_file_secret } = toRefs<any>(proxy?.useDict('sys_oper_type', 'sys_common_status', 'sys_user_secret', 'sys_file_secret'));
const operlogList = ref<OperLogVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<number | string>>([]);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const defaultSort = ref<any>({ prop: 'operTime', order: 'descending' });

const operLogTableRef = ref<ElTableInstance>();
const queryFormRef = ref<ElFormInstance>();
const secretFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 密级选择对话框数据
const secretDialog = reactive({
  visible: false
});

const secretForm = reactive({
  secret: ''
});

const secretRules = {
  secret: [{ required: true, message: '请选择文件密级', trigger: 'change' }]
};

const data = reactive<PageData<OperLogForm, OperLogQuery>>({
  form: {
    operId: undefined,
    tenantId: undefined,
    title: '',
    businessType: 0,
    businessTypes: undefined,
    method: '',
    requestMethod: '',
    operatorType: 0,
    operName: '',
    operNickName: '',
    deptName: '',
    operUrl: '',
    operIp: '',
    operLocation: '',
    operParam: '',
    jsonResult: '',
    status: 0,
    errorMsg: '',
    operTime: '',
    costTime: 0,
    detail: '',
    secret: ''
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    operIp: '',
    title: '',
    operName: '',
    operNickName: '',
    businessType: '',
    status: '',
    orderByColumn: defaultSort.value.prop,
    isAsc: defaultSort.value.order,
    detail: '',
    secret: ''
  },
  rules: {}
});

const { queryParams, form } = toRefs(data);

// 过滤出低于或等于用户当前密级的密级选项
const filteredSysSecret = computed(() => {
  // 确保 sys_user_secret 数据已加载
  if (!sys_user_secret.value || !sys_user_secret.value.length) {
    return [];
  }
  
  // 获取当前用户密级（这里假设从用户信息中获取）
  const currentUserSecret = userInfo.value.secret; // 需要从实际用户信息中获取
  
  // 过滤出低于或等于用户当前密级的选项
  return sys_file_secret.value.filter((dict: any) => {
    // 只显示低于或等于用户当前密级的选项
    return parseInt(dict.value) <= parseInt(currentUserSecret);
  });
});

// 用户信息状态
const userInfo = ref<Partial<UserVO>>({});
const getUser = async() => {
  const res = await getUserProfile();
  userInfo.value = res.data.user;
}

/** 查询登录日志 */
const getList = async () => {
  loading.value = true;
  const res = await list(proxy?.addDateRange(queryParams.value, dateRange.value));
  operlogList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};
/** 操作日志类型字典翻译 */
const typeFormat = (row: OperLogForm) => {
  return proxy?.selectDictLabel(sys_oper_type.value, row.businessType);
};
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  operLogTableRef.value?.sort(defaultSort.value.prop, defaultSort.value.order);
};
/** 多选框选中数据 */
const handleSelectionChange = (selection: OperLogVO[]) => {
  ids.value = selection.map((item) => item.operId);
  multiple.value = !selection.length;
};
/** 排序触发事件 */
const handleSortChange = (column: any) => {
  queryParams.value.orderByColumn = column.prop;
  queryParams.value.isAsc = column.order;
  getList();
};

const operInfoDialogRef = ref<InstanceType<typeof OperInfoDialog>>();
/** 详细按钮操作 */
const handleView = (row: OperLogVO) => {
  operInfoDialogRef.value.openDialog(row);
};

/** 删除按钮操作 */
const handleDelete = async (row?: OperLogVO) => {
  const operIds = row?.operId || ids.value;
  await proxy?.$modal.confirm('是否确认删除日志编号为"' + operIds + '"的数据项?');
  await delOperlog(operIds);
  await getList();
  proxy?.$modal.msgSuccess('删除成功');
};

/** 清空按钮操作 */
const handleClean = async () => {
  await proxy?.$modal.confirm('是否确认清空所有操作日志数据项?');
  await cleanOperlog();
  await getList();
  proxy?.$modal.msgSuccess('清空成功');
};

/** 导出按钮操作 */
const handleExport = () => {
  // 显示密级选择对话框
  secretDialog.visible = true;
};

/** 提交密级选择表单 */
const submitSecretForm = async () => {
  const valid = await secretFormRef.value.validate();
  if (valid) {
    // 获取选中的密级标签
    const secretLabel = sys_user_secret.value.find((item: any) => item.value === secretForm.secret)?.label || '未知密级';
    
    proxy?.download(
      'monitor/operlog/export',
      {
        ...queryParams.value
      },
      `config_${secretLabel}_${new Date().getTime()}.xlsx`
    );
    
    // 关闭对话框并重置表单
    secretDialog.visible = false;
    secretForm.secret = '';
  }
};

/** 取消密级选择表单 */
const cancelSecretForm = () => {
  secretDialog.visible = false;
  secretForm.secret = '';
  secretFormRef.value?.resetFields();
};

onMounted(() => {
  getList();
  getUser();
});
</script>

<style scoped>
.detail_css {
  font-size: 14px;
  font-family: inherit;
  line-height: 2;
  color: inherit;
}

.detail_css {
  font-size: 14px;
  font-family: inherit;
  line-height: 2;
  color: inherit;
}
</style>

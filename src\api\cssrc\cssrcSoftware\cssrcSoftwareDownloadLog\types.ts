export interface CssrcSoftwareDownloadLogVO {
  /**
   * 日志ID
   */
  logId: string | number;

  /**
   * 软件ID
   */
  softwareId: string | number;
  softwareName: string;

  /**
   * 版本ID
   */
  versionId: string | number;
  versionName: string;

  /**
   * 下载用户ID
   */
  userId: string | number;
  nickName: string;

  /**
   * 下载IP
   */
  ipAddress: string;

  /**
   * 下载时间
   */
  downloadTime: string;

  /**
   * 下载状态
   */
  status: string;

}

export interface CssrcSoftwareDownloadLogForm extends BaseEntity {
  /**
   * 日志ID
   */
  logId?: string | number;

  /**
   * 软件ID
   */
  softwareId?: string | number;
  softwareName: string;

  /**
   * 版本ID
   */
  versionId?: string | number;
  versionName: string;

  /**
   * 下载用户ID
   */
  userId?: string | number;
  nickName: string;
  
  /**
   * 下载IP
   */
  ipAddress?: string;

  /**
   * 下载时间
   */
  downloadTime?: string;

  /**
   * 下载状态
   */
  status?: string;

}

export interface CssrcSoftwareDownloadLogQuery extends PageQuery {

  /**
   * 软件ID
   */
  softwareId?: string | number;
  softwareName: string;

  /**
   * 版本ID
   */
  versionId?: string | number;
  versionName: string;
  /**
   * 下载用户ID
   */
  userId?: string | number;
  nickName: string;

  /**
   * 下载IP
   */
  ipAddress?: string;

  /**
   * 下载时间
   */
  downloadTime?: string;

  /**
   * 下载状态
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}




import request from '@/utils/request'
import { AxiosPromise } from 'axios'

/**
 * 软件分类树节点
 */
export interface SoftwareCategoryNode {
  categoryId: number
  categoryName: string
  parentId: number
  children?: SoftwareCategoryNode[]
}

/**
 * 软件信息
 */
export interface SoftwareInfo {
  softwareId: number
  categoryId: number
  softwareName: string
  manufacturer?: string
  country?: string
  intro?: string
  downloadCount?: number
  status: string
}

/**
 * 软件版本信息
 */
export interface SoftwareVersion {
  versionId: number
  softwareId: number
  versionName: string
  platform: string
  architecture?: string
  packageType?: string
  bits?: string
  ossId: string
  fileSize?: number
  remark?: string
  downloadCount?: number
  status: string
  displayStatus: string
  approvalStatus: string
}

/**
 * 软件评论
 */
export interface SoftwareComment {
  commentId: number
  softwareId: number
  userId: number
  userName?: string
  nickName?: string
  content: string
  replyTo?: number
  status: string
  createTime: string
  likeCount?: number
  userLikeType?: number
  children?: SoftwareComment[]
  replyToUserId?: number
  replyToUserName?: string
}

/**
 * 分页查询参数
 */
export interface PageQuery {
  pageNum: number
  pageSize: number
  categoryId?: number
  softwareName?: string
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  rows: T[]
  total: number
}

/**
 * 获取软件分类树
 */
export const getSoftwareCategories = (): AxiosPromise<SoftwareCategoryNode[]> => {
  return request({
    url: '/public/software/categories',
    method: 'get'
  })
}

/**
 * 获取软件列表
 */
export const getSoftwareList = (params: PageQuery): AxiosPromise<PageResult<SoftwareInfo>> => {
  return request({
    url: '/public/software/list',
    method: 'get',
    params
  })
}

/**
 * 获取软件详情
 */
export const getSoftwareDetail = (softwareId: number): AxiosPromise<SoftwareInfo> => {
  return request({
    url: `/public/software/${softwareId}`,
    method: 'get'
  })
}

/**
 * 获取软件版本列表
 */
export const getSoftwareVersions = (softwareId: number): AxiosPromise<SoftwareVersion[]> => {
  return request({
    url: `/public/software/${softwareId}/versions`,
    method: 'get'
  })
}

/**
 * 获取软件版本详情
 */
export const getVersionDetail = (versionId: number): AxiosPromise<SoftwareVersion> => {
  return request({
    url: `/public/software/version/${versionId}`,
    method: 'get'
  })
}

/**
 * 下载软件
 */
export const downloadSoftware = (versionId: number): void => {
  window.open(`/public/software/download/${versionId}`, '_blank')
}

/**
 * 获取软件评论列表
 */
export const getVersionComments = (
  versionId: number,
  params: { pageNum: number; pageSize: number }
): AxiosPromise<PageResult<SoftwareComment>> => {
  return request({
    url: `/public/software/${versionId}/comments`,
    method: 'get',
    params
  })
}

/**
 * 添加软件评论
 */
export const addVersionComment = (
  versionId: number,
  data: { content: string }
): AxiosPromise<void> => {
  return request({
    url: `/public/software/${versionId}/comments`,
    method: 'post',
    data
  })
}

/**
 * 回复评论
 */
export const replyComment = (
  commentId: number,
  data: { content: string; versionId: number }
): AxiosPromise<void> => {
  return request({
    url: `/public/software/comments/${commentId}/reply`,
    method: 'post',
    data
  })
}

/**
 * 点赞/取消点赞评论
 */
export const toggleCommentLike = (
  commentId: number,
  likeType: number = 1
): AxiosPromise<void> => {
  return request({
    url: `/public/software/comments/${commentId}/like`,
    method: 'post',
    params: { likeType }
  })
}

/**
 * 获取评论点赞状态
 */
export const getCommentLikeStatus = (commentId: number): AxiosPromise<any> => {
  return request({
    url: `/public/software/comments/${commentId}/like-status`,
    method: 'get'
  })
}

/**
 * 获取用户下载历史
 */
export const getUserDownloadHistory = (
  params: PageQuery
): AxiosPromise<PageResult<any>> => {
  return request({
    url: '/public/software/download-history',
    method: 'get',
    params
  })
}

/**
 * 搜索软件
 */
export const searchSoftware = (
  keyword: string,
  params: PageQuery
): AxiosPromise<PageResult<SoftwareInfo>> => {
  return request({
    url: '/public/software/search',
    method: 'get',
    params: {
      ...params,
      keyword
    }
  })
}

/**
 * 获取软件统计数据
 */
export const getSoftwareStatistics = (): AxiosPromise<{
  totalSoftware: number
  totalVersions: number
  totalDownloads: number
  totalCategories: number
}> => {
  return request({
    url: '/public/software/statistics',
    method: 'get'
  })
}

/**
 * 获取热门软件排行
 */
export const getPopularSoftware = (limit: number = 10): AxiosPromise<SoftwareInfo[]> => {
  return request({
    url: '/public/software/popular',
    method: 'get',
    params: { limit }
  })
}


export interface LogArchiveVO {
    /** 
     * 配置ID
     */
    configId: string;
    /** 
     * 租户编号
     */
    tenantId: string;
    /** 
     * 保留月数(6/12/36)
     */
    retainMonths: number;
    /** 
     * 转存路径
     */
    archivePath: string;
    /** 
     * 自动转存
     */
    autoArchive: string;
    /** 
     * 最近转存时间
     */
    lastArchiveTime: string;
    /** 
     * 创建者
     */
    createBy: string;
    /** 
     * 创建部门
     */
    createDept: string;
    /** 
     * 创建时间
     */
    createTime: string;
    /** 
     * 更新者
     */
    updateBy: string;
    /** 
     * 更新时间
     */
    updateTime: string;
}
export interface LogArchiveForm extends BaseEntity { 
    /** 
     * 配置ID
     */
    configId: string;
    /** 
     * 租户编号
     */
    tenantId: string;
    /** 
     * 保留月数(6/12/36)
     */
    retainMonths: number;
    /** 
     * 转存路径
     */
    archivePath: string;
    /** 
     * 自动转存
     */
    autoArchive: string;
    /** 
     * 最近转存时间
     */
    lastArchiveTime: string;
    /** 
     * 创建者
     */
    createBy: string;
    /** 
     * 创建部门
     */
    createDept: string;
    /** 
     * 创建时间
     */
    createTime: string;
    /** 
     * 更新者
     */
    updateBy: string;
    /** 
     * 更新时间
     */
    updateTime: string;
}
export interface LogArchiveQuery extends PageQuery { 
    /** 
     * 保留月数(6/12/36)
     */
    retainMonths: number;
    /** 
     * 转存路径
     */
    archivePath: string;
    /** 
     * 自动转存
     */
    autoArchive: string;
    /** 
     * 最近转存时间
     */
    lastArchiveTime: string;
}
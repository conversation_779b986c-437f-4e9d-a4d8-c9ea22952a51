<template>
  <el-container class="software-detail">
    <!-- 软件基本信息头部 -->
    <el-header class="software-header">
      <div class="software-icon">
        <el-icon size="64"><Box /></el-icon>
      </div>
      <div class="software-info">
        <h1 class="software-name">{{ software.softwareName }}</h1>
        <div class="software-meta">
          <div class="meta-item" v-if="software.manufacturer">
            <el-icon><OfficeBuilding /></el-icon>
            <span>{{ software.manufacturer }}</span>
          </div>
          <div class="meta-item" v-if="software.country">
            <el-icon><Location /></el-icon>
            <span>{{ software.country }}</span>
          </div>
          <div class="meta-item">
            <el-icon><Download /></el-icon>
            <span>{{ software.downloadCount || 0 }}次下载</span>
          </div>
        </div>
        <p class="software-intro" v-if="software.intro">{{ software.intro }}</p>
      </div>
    </el-header>

    <!-- 版本列表 -->
    <el-main class="version-section" ref="versionsRef">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><Collection /></el-icon>
          可用版本
        </h2>
        <div class="version-filters">
          <el-select v-model="platformFilter" placeholder="选择平台" clearable style="width: 120px">
            <el-option label="Windows" value="Windows" />
            <el-option label="Linux" value="Linux" />
            <el-option label="macOS" value="macOS" />
            <el-option label="Android" value="Android" />
          </el-select>
          <el-select v-model="archFilter" placeholder="选择架构" clearable style="width: 120px">
            <el-option label="x86" value="x86" />
            <el-option label="x64" value="x64" />
            <el-option label="arm64" value="arm64" />
          </el-select>
        </div>
      </div>

      <div v-loading="versionsLoading" class="versions-container">
        <div v-if="filteredVersions.length === 0" class="no-versions">
          <el-empty description="暂无可用版本" />
        </div>
        <!-- 版本列表 -->
        <div v-else class="versions-list">
          <el-card
            v-for="version in filteredVersions"
            :key="version.versionId"
            class="version-card"
            :class="{ 'version-active': selectedVersionId === version.versionId }"
            @click="selectVersion(version)"
            shadow="hover"
          >
            <template #header>
              <div class="version-header">
                <div class="version-info">
                  <h3 class="version-name">{{ version.versionName }}</h3>
                  <div class="version-tags">
                    <el-tag :type="getPlatformTagType(version.platform)" size="small">
                      {{ version.platform }}
                    </el-tag>
                    <el-tag v-if="version.architecture" type="info" size="small">
                      {{ version.architecture }}
                    </el-tag>
                    <el-tag v-if="version.bits" type="warning" size="small">
                      {{ version.bits }}位
                    </el-tag>
                  </div>
                </div>
                <div class="version-actions" @click.stop>
                  <el-button
                    type="primary"
                    :loading="downloadingVersions.has(version.versionId)"
                    @click="handleDownload(version)"
                  >
                    <el-icon class="el-icon--left"><Download /></el-icon>下载
                  </el-button>
                  <el-button
                    type="info"
                    plain
                    @click="showCommentDialog(version)"
                  >
                    <el-icon class="el-icon--left"><ChatDotRound /></el-icon>评论
                  </el-button>
                </div>
              </div>
            </template>
            <div class="version-details">
              <el-row class="detail-grid">
                <el-col :span="6" class="detail-item" v-if="version.packageType">
                  <el-text class="label">包类型：{{ version.packageType }} </el-text>
                </el-col>
                <el-col :span="6" class="detail-item" v-if="version.fileSize">
                  <el-text class="label">文件大小：{{ formatFileSize(version.fileSize) }} </el-text>
                </el-col>
                <el-col :span="6" class="detail-item">
                  <el-text class="label">下载次数：{{ version.downloadCount || 0 }} </el-text>
                </el-col>
                <el-col :span="6" class="detail-item" v-if="version.updateTime">
                  <el-text class="label">更新时间：{{ parseTime(version.updateTime) }} </el-text>
                </el-col>
              </el-row>
              <div class="version-remark" v-if="version.remark">
                <el-icon><InfoFilled /></el-icon>
                <span>{{ version.remark }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-main>

    <!-- 评论列表 -->
    <el-main v-loading="commentsLoading" class="comments-container">
      <h2 class="section-title">
        <el-icon><ChatDotRound /></el-icon>
        用户评论 ({{ commentTotal }})
      </h2>

      <div v-if="comments.length === 0" class="no-comments">
        <el-empty description="暂无评论，快来发表第一条评论吧！" />
      </div>
      <div v-else class="comments-list">
        <!-- 递归渲染评论树 -->
        <SoftwareDetailComment
          v-for="comment in comments"
          :key="comment.commentId"
          :comment="comment"
          :level="0"
          @reply="handleReplyComment"
          @like="handleCommentLike"
        />
      </div>

      <!-- 分页 -->
      <div v-if="commentTotal > 0" class="comment-pagination">
        <el-pagination
          v-model:current-page="commentQuery.pageNum"
          v-model:page-size="commentQuery.pageSize"
          :page-sizes="[5, 10, 20]"
          :total="commentTotal"
          layout="total, sizes, prev, pager, next"
          @size-change="handleCommentSizeChange"
          @current-change="handleCommentCurrentChange"
        />
      </div>
    </el-main>

    <!-- 评论对话框 -->
    <el-dialog
      v-model="commentDialog.visible"
      :title="`${commentDialog.version?.versionName} - 版本评论`"
      width="60%"
      class="comment-dialog"
    >
      <div class="comment-input-section">
        <div class="mb-4">
        <el-input
          v-model="newComment"
          type="textarea"
          :rows="3"
          placeholder="请输入您对此版本的评论..."
          maxlength="500"
          show-word-limit
        /></div>
        <div class="mb-4">
          <el-button
            type="primary"
            :loading="submittingComment"
            @click="handleSubmitComment"
          >
            发表评论
          </el-button>
        </div>
      </div>
    </el-dialog>
  </el-container>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick, getCurrentInstance, defineComponent } from 'vue'
import type { ComponentInternalInstance } from 'vue'
import { watch } from 'vue'
import { ElMessage, ElMessageBox, ElAvatar, ElIcon, ElButton } from 'element-plus'
import {
  Box,
  OfficeBuilding,
  Location,
  Download,
  Collection,
  ChatDotRound,
  // Top,
  // User,
  InfoFilled
} from '@element-plus/icons-vue'
import {
  getSoftwareVersions,
  getVersionComments,
  addVersionComment,
  replyComment,
  toggleCommentLike
} from '@/api/cssrc/cssrcSoftware/public'
import request from '@/utils/request'
import { parseTime } from '@/utils/ruoyi'
import SoftwareDetailComment from './SoftwareDetailComment.vue'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
// Props
const props = defineProps<{
  software: any
}>()

// Emits
const emit = defineEmits<{
  close: []
  downloadSuccess: [software: any]
}>()

// 响应式数据
const versionsRef = ref()
const versions = ref([] as any[])
const versionsLoading = ref(false)
const downloadingVersions = ref(new Set())
const platformFilter = ref('')
const archFilter = ref('')

// 选中的版本ID
const selectedVersionId = ref<number | null>(null)

// 评论相关
const comments = ref([])
const commentsLoading = ref(false)
const commentTotal = ref(0)
const newComment = ref('')
const submittingComment = ref(false)

// 评论对话框
const commentDialog = reactive({
  visible: false,
  version: null as any
})

// 评论查询参数
const commentQuery = reactive({
  pageNum: 1,
  pageSize: 10,
  softwareId: props.software.softwareId
})

// 过滤后的版本列表
const filteredVersions = computed(() => {
  let filtered = versions.value
  
  if (platformFilter.value) {
    filtered = filtered.filter(v => v.platform === platformFilter.value)
  }
  
  if (archFilter.value) {
    filtered = filtered.filter(v => v.architecture === archFilter.value)
  }
  
  return filtered
})

// 获取软件版本
const getVersions = async () => {
  versionsLoading.value = true
  try {
    const { data } = await getSoftwareVersions(props.software.softwareId)
    versions.value = Array.isArray(data)
      ? data.filter(v => v.status === '0' && v.displayStatus === '0')
      : []
    // 自动选中第一个版本
    if (versions.value.length > 0) {
      selectedVersionId.value = versions.value[0].versionId
      getComments()
    }
  } catch (error) {
    console.error('获取版本列表失败:', error)
    ElMessage.error('获取版本列表失败')
    versions.value = []
  } finally {
    versionsLoading.value = false
  }
}


// 获取版本评论
const getComments = async () => {
  if (!selectedVersionId.value) return

  commentsLoading.value = true
  try {
    const response: any = await getVersionComments(selectedVersionId.value, {
      pageNum: commentQuery.pageNum,
      pageSize: commentQuery.pageSize
    })
    const rawComments = response.rows || []
  
    // 构建评论树形结构
    comments.value = buildCommentTree(rawComments)
    commentTotal.value = response.total || 0
  } catch (error) {
    console.error('获取版本评论失败:', error)
    ElMessage.error('获取版本评论失败')
  } finally {
    commentsLoading.value = false
  }
}

// 构建评论树形结构
const buildCommentTree = (rawComments: any[]) => {
  const commentMap = new Map()
  const rootComments: any[] = []

  // 先将所有评论放入map，并初始化children数组
  rawComments.forEach(comment => {
    comment.children = []
    commentMap.set(comment.commentId, comment)
  })

  // 构建树形结构
  rawComments.forEach(comment => {
    if (comment.replyTo && comment.replyTo !== 0) {
      // 这是一个回复评论，获取被回复用户的信息
      const parentComment = commentMap.get(comment.replyTo)
      if (parentComment) {
        comment.replyToUserId = parentComment.userId
        comment.replyToUserName = parentComment.nickName || parentComment.userName || `用户${parentComment.userId}`
        parentComment.children.push(comment)
      } else {
        // 如果找不到父评论，作为根评论处理
        rootComments.push(comment)
      }
    } else {
      // 这是一个根评论
      rootComments.push(comment)
    }
  })

  // 按时间排序根评论（最新的在前）
  rootComments.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())

  // 递归排序子评论（最早的在前，符合对话逻辑）
  const sortChildren = (comments: any[]) => {
    comments.forEach(comment => {
      if (comment.children && comment.children.length > 0) {
        comment.children.sort((a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime())
        sortChildren(comment.children)
      }
    })
  }
  sortChildren(rootComments)

  return rootComments
}

// 选择版本
const selectVersion = (version: any) => {
  selectedVersionId.value = version.versionId
  // 重置分页到第一页
  commentQuery.pageNum = 1
  getComments()
}


// 显示评论对话框
const showCommentDialog = (version: any) => {
  commentDialog.version = version
  commentDialog.visible = true
  newComment.value = ''
}

// 提交评论
const handleSubmitComment = async () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  if (!selectedVersionId.value) {
    ElMessage.warning('请先选择一个版本再发表评论')
    return
  }

  submittingComment.value = true
  try {
    await addVersionComment(commentDialog.version?.versionId || selectedVersionId.value, {
      content: newComment.value.trim()
    })
    ElMessage.success('评论发表成功')
    newComment.value = ''
    commentDialog.visible = false
    getComments() // 刷新评论
  } catch (error) {
    console.error('发表评论失败:', error)
    ElMessage.error('发表评论失败，请先登录')
  } finally {
    submittingComment.value = false
  }
}


// 处理版本下载
const handleDownload = async (version: any) => {
  downloadingVersions.value.add(version.versionId);

  try {
    // 调用后端下载接口，设置正确的响应类型为blob
    const response = await request({
      url: `/public/software/download/${version.versionId}`,
      method: 'get',
      responseType: 'blob'  // 关键：设置响应类型为blob以处理文件下载
    });

    // 创建下载链接并触发下载
    const blob = new Blob([response.data], { type: 'application/octet-stream' });
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;

    // 尝试从响应头获取文件名，如果没有则使用默认名称
    let fileName = `${version.versionName || 'software'}_${version.versionNumber || '1.0'}_${version.platform}_${version.architecture}`;

    // 优先从响应头获取文件名
    if (response.headers && response.headers['download-filename']) {
      fileName = decodeURIComponent(response.headers['download-filename']);
    } else if (version.fileName) {
      // 如果响应头没有文件名，使用版本中的文件名信息
      const extension = version.fileName.split('.').pop();
      if (extension) {
        fileName += `.${extension}`;
      }
    }

    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    ElMessage.success('下载开始，请稍候...');

    // 延迟3秒后刷新版本列表，获取最新的下载次数
    setTimeout(async () => {
      try {
        await getVersions();
        emit('downloadSuccess', props.software);
        ElMessage.success('下载次数已更新');
      } catch (error) {
        console.error('刷新版本列表失败:', error);
        // 如果刷新失败，至少更新本地显示
        version.downloadCount = (version.downloadCount || 0) + 1;
        emit('downloadSuccess', props.software);
      }
    }, 3000);

  } catch (error) {
    console.error('下载失败:', error);
    ElMessage.error('下载失败，请稍后重试');
  } finally {
    // 延迟移除下载状态，给用户一些视觉反馈
    setTimeout(() => {
      downloadingVersions.value.delete(version.versionId);
    }, 1500);
  }
}


// 处理评论点赞
const handleCommentLike = async (comment: any, likeType: number) => {
  try {
    await toggleCommentLike(comment.commentId, likeType)
    // 重新获取当前版本的评论
    getComments()
  } catch (error) {
    console.error('点赞失败:', error)
    ElMessage.error('操作失败，请先登录')
  }
}

// 处理回复评论
const handleReplyComment = (comment: any) => {
  ElMessageBox.prompt('请输入回复内容', '回复评论', {
    confirmButtonText: '发送',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputValidator: (value) => {
      if (!value || !value.trim()) {
        return '回复内容不能为空'
      }
      return true
    }
  }).then(async ({ value }) => {
    try {
      await replyComment(comment.commentId, {
        content: value.trim(),
        versionId: selectedVersionId.value
      })
      ElMessage.success('回复成功')
      getComments()
    } catch (error) {
      console.error('回复失败:', error)
      ElMessage.error('回复失败，请先登录')
    }
  }).catch(() =>  {
    // 用户取消回复
  })
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (!bytes) return '未知'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 获取平台标签类型
const getPlatformTagType = (platform: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'Windows': 'primary',
    'Linux': 'success',
    'macOS': 'warning',
    'Android': 'info'
  }
  return typeMap[platform] || 'primary'
}



// 评论分页处理
const handleCommentSizeChange = (size: number) => {
  commentQuery.pageSize = size
  commentQuery.pageNum = 1
  getComments()
}

const handleCommentCurrentChange = (page: number) => {
  commentQuery.pageNum = page
  getComments()
}

watch(
  () => selectedVersionId.value,
  (newVal) => {
    if (newVal) {
      commentQuery.pageNum = 1
      getComments()
    }
  }
)


// 初始化
onMounted(() => {
  getVersions()
  getComments()
})

// 注册组件
const components = {
  SoftwareDetailComment
}
</script>

<style scoped lang="scss">
.software-detail {
  padding: 0;

  .software-header {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    padding: 24px;
    height: auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 24px;

    .software-icon {
      color: rgba(255, 255, 255, 0.9);
    }

    .software-info {
      flex: 1;

      .software-name {
        margin: 0 0 16px 0;
        font-size: 28px;
        font-weight: 700;
        color: white;
      }

      .software-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 16px;

        .meta-item {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.9);

          .el-icon {
            margin-right: 6px;
          }
        }
      }

      .software-intro {
        margin: 0;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;
      }
    }

    .software-actions {
      .el-button {
        padding: 12px 24px;
        font-size: 16px;
      }
    }
  }

  .version-section, .comment-section {
    padding: 0 24px 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .section-title {
        display: flex;
        align-items: center;
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #303133;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .version-filters {
        display: flex;
        gap: 12px;
      }
    }

    .versions-container {
      min-height: 200px;
    }

    .versions-list {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .version-card {
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        background-color: #fff;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          border-color: #409eff;
        }

        .version-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 20px;

          .version-info {
            .version-name {
              margin: 0 0 8px 0;
              font-size: 18px;
              font-weight: 600;
              color: #303133;
            }

            .version-tags {
              display: flex;
              gap: 8px;
            }
          }

          .version-actions {
            display: flex;
            gap: 8px;
          }
        }

        .version-details {
          padding: 10px 20px;

          .detail-grid {
            .detail-item {
              display: flex;
              font-size: 14px;
              .label {
                color: #909399;
              }
              .value {
                color: #606266;
              }
            }
          }

          .version-remark {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 13px;
            color: #606266;

            .el-icon {
              margin-right: 6px;
              color: #909399;
            }
          }
        }
      }
    }

    .comment-input-section {
      margin-bottom: 24px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
    }

    .comments-container {
      min-height: 200px;
    }

    .comment-pagination {
      margin-top: 24px;
      text-align: center;
    }

    .no-versions, .no-comments {
      padding: 40px 0;
      text-align: center;
    }
  }
}

.comment-dialog {
  .version-comment-section {
    .comment-input-section {
      margin-bottom: 20px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
    }

    .comments-list {
      .comment-item {
        display: flex;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .comment-content {
          flex: 1;

          .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;

            .comment-author {
              font-weight: 500;
              color: #303133;
              font-size: 14px;
            }

            .comment-time {
              font-size: 12px;
              color: #909399;
            }
          }

          .comment-text {
            margin-bottom: 8px;
            color: #606266;
            line-height: 1.5;
            font-size: 14px;
          }

          .comment-actions {
            display: flex;
            gap: 12px;

            .is-liked {
              color: #409eff;
            }
          }
        }
      }
    }

    .comment-pagination {
      margin-top: 16px;
      text-align: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .software-detail {
    .software-header {
      flex-direction: column;
      text-align: center;
      padding: 20px 16px;

      .software-info {
        .software-name {
          font-size: 24px;
        }

        .software-meta {
          justify-content: center;
        }
      }
    }

    .version-section, .comment-section {
      padding: 0 16px 20px;

      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .version-filters {
          width: 100%;
          justify-content: space-between;
        }
      }

      .versions-list {
        .version-card {
          .version-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .version-actions {
              width: 100%;
              justify-content: space-between;
            }
          }

          .version-details {
            .detail-grid {
              grid-template-columns: 1fr;
            }
          }
        }
      }
    }
  }
}

.version-card {
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &.version-active {
    border-color: #409eff;
    background-color: #f5f7fa;
  }
}

.comments-container {
  margin-top: 32px;
  padding: 28px;
  background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #e4e7ed;

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    font-size: 20px;
    font-weight: 700;
    color: #303133;
    padding-bottom: 12px;
    border-bottom: 2px solid #f0f0f0;

    .el-icon {
      margin-right: 10px;
      color: #409eff;
      font-size: 22px;
    }
  }

  .no-comments {
    text-align: center;
    padding: 60px 20px;

    .el-empty {
      .el-empty__description {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

.comment-header {
  margin-bottom: 8px;
  font-size: 14px;
  color: #303133;
  .comment-time {
    color: #909399;
  }
}

.comment-text {
  margin-bottom: 12px;
  color: #606266;
  line-height: 1.6;
}

.comment-pagination {
  margin-top: 16px;
  text-align: center;
  .el-pagination {
    font-size: 13px;
  }
}

.comment-author {
  font-weight: 600;
  color: #409eff;
}

</style>


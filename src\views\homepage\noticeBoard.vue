<template>
<div>
<el-row class="notice-board-container">
    <el-col :span="16">      
        <el-card shadow="hover" class="notice-board">
            <el-button 
                v-if="activeName === 'notice'"
                type="primary" 
                size="small" 
                circle 
                icon="Search" 
                @click="toggleSearch"
                class="search-button"
            ></el-button>
            <el-button 
                v-if="activeName === 'news'"
                type="primary" 
                size="small" 
                circle 
                icon="Search" 
                @click="toggleNewsSearch"
                class="search-button"
            ></el-button>
            <el-tabs v-model="activeName" style="margin-top: -10px;">
                <el-tab-pane name="notice">
                    <template #label>
                        <div class="notice-board-title">
                            <el-icon><chat-line-square /></el-icon>
                            <span class="label-span">通知公告</span>
                        </div>
                    </template>
                    <transition 
                        :enter-active-class="proxy?.animate.searchAnimate.enter" 
                        :leave-active-class="proxy?.animate.searchAnimate.leave"> <!-- proxy和animate是否存在的话，才执行进入和离开的动画 -->
                        <div>
                            <div v-if="isSearchVisible" v-show="showSearch" class="mb-[10px]">
                                <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                                    <el-form-item label="公告标题" prop="noticeTitle">
                                        <el-input v-model="queryParams.noticeTitle" placeholder="请输入公告标题" clearable @keyup.enter="handleQuery" />
                                    </el-form-item>
                                    <el-form-item label="类型" prop="noticeType">
                                        <el-select v-model="queryParams.noticeType" placeholder="公告类型" clearable>
                                            <el-option v-for="dict in sys_notice_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item style="margin-right: 0;">
                                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                                    </el-form-item>
                                </el-form>
                            </div>
                        </div>
                    </transition>
                    <ul v-loading="loading" class="notice-board-content">
                        <li v-for="notice in noticeList" :key="notice.noticeId">
                            <a style="cursor: pointer;" @click.prevent="handleViewDetail(notice.noticeId)">
                            <dict-tag label="公告类型" :options="sys_notice_type" :value="notice.noticeType" />
                            <span class="title" label="公告标题">{{ notice.noticeTitle }}
                                <el-tag v-if="isTopNotice(notice)" size="small" type="warning" prop="isTop">Top</el-tag>
                                <el-tag v-if="isNewNotice(notice.updateTime)" size="small" type="danger">New</el-tag>
                                <el-tag v-if="!isReadNotice(notice)" type="warning">未读</el-tag>
                                <el-tag v-if="isReadNotice(notice)" type="info">已读</el-tag>
                            </span>
                            <span class="createname" label="创建者">{{ notice.createByNickName }}</span>
                            <span label="创建时间">{{ parseTime(notice.updateTime, '{y}-{m}-{d}') }}</span>
                        </a></li>
                    </ul>
                    <pagination 
                        v-show="total > 0" 
                        v-model:page="queryParams.pageNum" 
                        v-model:limit="queryParams.pageSize" 
                        :total="total" 
                        @pagination="getList" 
                        style="margin-top:10px;" 
                    />
                </el-tab-pane>
                <el-tab-pane name="news">
                    <template #label>
                        <div class="notice-board-title">
                            <el-icon><postcard /></el-icon>
                            <span class="label-span">新闻动态</span>
                        </div>
                    </template>
                    <!-- <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                        <div v-if="isSearchVisible" v-show="showSearch" class="mb-[10px]">
                            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                                <el-form-item label="公告标题" prop="noticeTitle">
                                    <el-input v-model="queryParams.noticeTitle" placeholder="请输入公告标题" clearable @keyup.enter="handleQuery" />
                                </el-form-item>
                                <el-form-item label="发布人" prop="createByName">
                                    <el-input v-model="queryParams.createByName" placeholder="请输入发布人" clearable @keyup.enter="handleQuery" />
                                </el-form-item>
                                <el-form-item label="类型" prop="noticeType">
                                    <el-select v-model="queryParams.noticeType" placeholder="公告类型" clearable>
                                        <el-option v-for="dict in sys_notice_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                    </transition>
                    <ul v-loading="loading" class="notice-board-content">
                        <li v-for="notice in noticeList" :key="notice.noticeId">
                            <a style="cursor: pointer;" @click.prevent="handleViewDetail(notice.noticeId)">
                            <dict-tag label="公告类型" :options="sys_notice_type" :value="notice.noticeType" />
                            <span class="title" label="公告标题">{{ notice.noticeTitle }}
                                <el-tag v-if="isTopNotice(notice)" size="small" type="warning" prop="isTop">Top</el-tag>
                                <el-tag v-if="isNewNotice(notice.createTime)" size="small" type="danger">New</el-tag>
                                <el-tag v-if="!isReadNotice(notice)" type="warning">未读</el-tag>
                                <el-tag v-if="isReadNotice(notice)" type="info">已读</el-tag>
                            </span>
                            <span class="createname" label="创建者">{{ notice.createByName }}</span>
                            <span label="创建时间">{{ parseTime(notice.createTime, '{y}-{m}-{d}') }}</span>
                        </a></li>
                    </ul>
                    <pagination 
                        v-show="total > 0" 
                        v-model:page="queryParams.pageNum" 
                        v-model:limit="queryParams.pageSize" 
                        :total="total" 
                        @pagination="getList" 
                        style="margin-top:10px;" 
                    /> -->
                </el-tab-pane>
            </el-tabs>
        </el-card>
    </el-col>
    <el-col :span="8">
        <el-card shadow="hover" class="notice-board" style="margin:14px 10px 10px 10px; max-height:588px; overflow-y: scroll;"> 
        <div class="icon-board">
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color1">
                    <font-awesome-icon icon="gears" class="custom-icon-size"/>
                </div>
                <span class="button-text">试验数据管理</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color">
                    <font-awesome-icon icon="envelope" class="custom-icon-size"/>
                </div>
                <span class="button-text">安全邮件系统</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color2">
                    <font-awesome-icon icon="newspaper" class="custom-icon-size"/>
                </div>
                <span class="button-text">知识库</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color1">
                    <font-awesome-icon icon="floppy-disk" class="custom-icon-size"/>
                </div>
                <span class="button-text">软件白名单</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color1">
                    <font-awesome-icon icon="server" class="custom-icon-size"/>
                </div>
                <span class="button-text">软件资源库</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color">
                    <font-awesome-icon icon="book" class="custom-icon-size"/>
                </div>
                <span class="button-text">数字图书馆</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color2">
                    <font-awesome-icon icon="folder-open" class="custom-icon-size"/>
                </div>
                <span class="button-text">数字档案馆</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color1">
                    <font-awesome-icon icon="ship" class="custom-icon-size"/>
                </div>
                <span class="button-text">数值船海</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color1">
                    <font-awesome-icon icon="folder-tree" class="custom-icon-size"/>
                </div>
                <span class="button-text">软件质量管理</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color">
                    <font-awesome-icon icon="print" class="custom-icon-size"/>
                </div>
                <span class="button-text">打印刻录系统</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color2">
                    <font-awesome-icon icon="sitemap" class="custom-icon-size"/>
                </div>
                <span class="button-text">终端备份系统</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color1">
                    <font-awesome-icon icon="layer-group" class="custom-icon-size"/>
                </div>
                <span class="button-text">高性能计算</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color1">
                    <font-awesome-icon icon="chart-pie" class="custom-icon-size"/>
                </div>
                <span class="button-text">保密管理看板</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color">
                    <font-awesome-icon icon="piggy-bank" class="custom-icon-size"/>
                </div>
                <span class="button-text">财务管理系统</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color2">
                    <font-awesome-icon icon="cloud-arrow-up" class="custom-icon-size"/>
                </div>
                <span class="button-text">智能计算</span>
            </div>
            <div class="icon-board-item">
                <div class="icon-board-class icon-board-class-color1">
                    <font-awesome-icon icon="scale-balanced" class="custom-icon-size"/>
                </div>
                <span class="button-text">法治领航</span>
            </div>
        </div>
    </el-card>
    </el-col>
</el-row>
<el-row>
    <el-col :span="16">
        <el-card shadow="hover" class="notice-board" style="margin-bottom: 5px;">
            <template #header>
                <div class="notice-board-title">
                    <div class="title-content">
                        <el-icon><edit /></el-icon>
                        <span class="notice-board-title-span">待办任务</span>
                    </div>
                    <el-button type="primary" circle size="small" icon="Search" @click="toggleTaskSearch"></el-button>
                </div>
            </template>
            <taskList ref="taskListRef" /> <!-- 使用ref获取子组件实例 -->
        </el-card>
    </el-col>
    <el-col :span="8">
        <el-card shadow="hover" class="notice-board" style="margin:14px 10px 10px 10px">
            <template #header>
                <div class="notice-board-title">
                    <div class="title-content">
                        <el-icon><postcard /></el-icon>
                        <span class="notice-board-title-span">站点公告</span>
                    </div>
                    <el-button type="primary" circle size="small" icon="Search" @click="toggleSearch"></el-button>
                </div>
            </template>
        </el-card>
    </el-col>
</el-row>
</div>
</template>

<script setup name="Notice" lang="ts">
import { ChatLineSquare, Postcard } from '@element-plus/icons-vue';
import { listNotice } from '@/api/management/notice2';
import { NoticeVO } from '@/api/management/notice2/types';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import { useRouter } from 'vue-router';
import { useTagsViewStore } from '@/store/modules/tagsView';
import taskList from '@/views/homepage/taskList.vue';

const noticeList = ref<NoticeVO[]>([]);
// 引入字典数据
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_notice_type } = toRefs<any>(proxy?.useDict('sys_notice_type'));
const loading = ref(true);
const router = useRouter();
const showSearch = ref(true);
const total = ref(0);
const activeName = ref('notice');


// 添加查询参数对象
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    noticeTitle: '',
    createByName: '',
    createByNickName: '',
    status: '',
    noticeType: '',
    isTop: '',
    isRead: '',
    topDay: '',
    flowStatus: 'finish',
    secret: ''
});
// 添加查询表单ref
const queryFormRef = ref<ElFormInstance>();
/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
};

// getList方法加入查询参数
const getList = async () => {
    loading.value = true;
    try {
        const res = await listNotice(queryParams.value);
        noticeList.value = res.rows;
        total.value = res.total;
    } finally {
        loading.value = false;
    }
};

// 判断是否为新通知(7天内为新通知)
const isNewNotice = (updateTime: string) => {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    return new Date(updateTime) > oneWeekAgo;
}

// 判断是否已读
const isReadNotice = (notice: NoticeVO) => {
    return notice.isRead;
}

// 判断是否置顶(createTime要改成updateTime)
const isTopNotice = (notice: NoticeVO) => {
    if (notice.isTop === '1') {
        const topDate = new Date(notice.updateTime);
        topDate.setDate(topDate.getDate() + notice.topDay);
        return topDate >= new Date();
    }
    return false;
}
// 查看详情方法
const handleViewDetail = async (noticeId: string | number) => {
    await useTagsViewStore().delCachedView(router.currentRoute.value);
    // 跳转到通知详情页面
    router.push ({
        path: '/homepage/noticeBoard-detail',
        query: {
            noticeId: noticeId,
            t: new Date().getTime() // 保留时间戳防止缓存
        }
    })
}
// const handleViewDetail = async (noticeId: string | number) => {
//     // 跳转到通知详情页面
//     const route = {
//         path: '/homepage/noticeBoard-detail',
//         query: {
//             noticeId: noticeId,
//             t: new Date().getTime() // 添加时间戳，确保每次都能新打开一个页面
//         }
//     }
//     window.open(router.resolve(route).href, '_blank');
// }

const isSearchVisible = ref(false);
const toggleSearch = () => {
    isSearchVisible.value = !isSearchVisible.value;
}
const toggleNewsSearch = () => {
    isSearchVisible.value = !isSearchVisible.value;
}
// 添加子组件的引用
const taskListRef = ref<InstanceType<typeof taskList>>();
const toggleTaskSearch = () => {
    taskListRef.value.isTaskSearchVisible = !taskListRef.value.isTaskSearchVisible;
}
// onBeforeUnmount钩子，在页面切换时处理组件的销毁和重新创建
onBeforeUnmount(() => {
    // 清理组件状态  
    loading.value = false;
    noticeList.value = [];
});

onMounted(() => {
    getList();
});
</script>

<style scoped>
    .notice-board-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }
    .search-button {
        position: relative;
        float: right;
        margin: -3px -5px 0 0;
    }
    .notice-board {
        margin: 14px 0 0 10px;
        min-height: 599px;
        display: flex;
        flex-direction: column;
        cursor: default;
        font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', DengXian, SimSun, 'Segoe UI', Tahoma, Helvetica, sans-serif;
    }
    .notice-board-title {
        display: flex;
        margin-top: -5px;
        justify-content: space-between;
        flex-direction: row;
        align-items: center;
        overflow: hidden;
    }
    .title-content {
        display: flex;
        align-items: center;
    }
    .notice-board-title-span {
        font-size: 1em;
        line-height: 1.1em;
        color: inherit;
    }
    .label-span {
        font-size: 1.125em;
    }
    .notice-board-title .el-icon {
        margin-right: 5px;
    }
    .notice-board-content { 
        padding-left: 10px;
        margin-top: 0px;
        list-style-type: none;
        color: #3c3c3c;
    }
    .notice-board-content li a{
        display: flex;
        flex-direction: row;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    .notice-board-content li span {
        display: inline-block;
        margin-left: 10px;
        font-size: 1em;
        white-space: nowrap; /*防止文字换行*/
        overflow: hidden;
        text-overflow: ellipsis; /*使用省略号表示被隐藏的文本*/
    }
    .notice-board-content li span.title {
        flex: 0.8;
    }
    .notice-board-content li span.createname {
        flex: 0.15;
    }
    .notice-board-content a:hover, a:active{
        text-decoration: none;
        color: rgba(64, 158, 255, 1);
    }
    .title .el-tag {
        display: inline-flex;
        margin-left: 5px;
        height: 20px;
        font-size: 12px;
        padding: 0 8px;
        align-items: center;
        vertical-align: middle;
        line-height: 1;
    }
    .notice-board .pagination {
        margin-right: 10px;
    }
    .icon-board {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }
    .icon-board-item {
        display: flex;
        flex: 0 1 calc(26% - 10px); /** 按比例扩展/按比例收缩/flex项目的初始宽度 */
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 122px;
        height: 105px;
        margin: 14px 0px;
        cursor: pointer;
        border: none;
        position: relative;
        overflow: visible;
    }
    .icon-board-item:nth-child(17) {
        flex: 0 0 calc(26% - 10px);
        justify-content: flex-start;
    }
    .icon-board-class {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 75px;
        height: 75px;
        margin-bottom: 3px;
        border-radius: 28px;
    }
    .icon-board-class-color {
        background: linear-gradient(to bottom right, rgba(255, 161, 109, 0.8), rgba(255, 68, 148, 0.8));
        /* background: linear-gradient(to bottom right, rgba(250, 182, 182, 0.9), rgba(245, 108, 108, 0.9)); */
        /* background: linear-gradient(to bottom right, rgba(255, 197, 161, 0.9), rgba(255, 118, 166, 0.9)); */
        box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.2); 
    }
    .icon-board-class-color1 {
        /* background: linear-gradient(to bottom right, rgba(43, 233, 254, 0.9), rgba(26, 180, 254, 0.9)); */
        background: linear-gradient(to bottom right, rgba(160, 207, 255, 0.9), rgba(65, 158, 255, 0.9));
        box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.2); 
    }
    .icon-board-class-color2 {
        /* background: linear-gradient(to bottom right, rgba(125, 233, 184, 0.9), rgba(44, 202, 149, 0.9)); */
        background: linear-gradient(to bottom right, rgba(179, 225, 157, 0.9), rgba(103, 194, 58, 0.9));
        box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.2); 
    }
    .icon-board-class-color:hover,
    .icon-board-class-color:active {
        background: linear-gradient(to bottom right, rgba(255, 161, 109, 0.6), rgba(255, 68, 148, 0.6));
        /* background: linear-gradient(to bottom right, rgba(250, 182, 182, 0.6), rgba(245, 108, 108, 0.6)); */
        box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
    }
    .icon-board-class-color1:hover,
    .icon-board-class-color1:active {
        background: linear-gradient(to bottom right, rgba(43, 233, 254, 0.6), rgba(26, 180, 254, 0.6));
        box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.2); 
    }
    .icon-board-class-color2:hover,
    .icon-board-class-color2:active {
        background: linear-gradient(to bottom right, rgba(125, 233, 184, 0.6), rgba(44, 202, 149, 0.6));
        /* background: linear-gradient(to bottom right, rgba(179, 225, 157, 0.6), rgba(103, 194, 58, 0.6)); */
        box-shadow: 2px 4px 10px rgba(0, 0, 0, 0.2); 
    }
    .button-text {
        display: block;
        width: 124px;
        height: 24px;
        margin-top: 5px; /* 调整文本与图标的间距 */
        text-align: center; /* 文本居中 */
        color:#3c3c3c;
    }
    .custom-icon-size {
        color: #fff;
        font-size: 32px; 
    }

    @media (max-width: 768px) {
        .notice-board-content li span.title {
            flex: 0.5;
            max-width: 40%;
        }
        .notice-board-content li span.createname {
            flex: 0.05;
            max-width: 15%;
        }
    }
    @media (max-width: 576px) {
        .notice-board-content li span.title {
            flex: 0.5;
            max-width: 30%;
        }

        .notice-board-content li span.createname {
            flex: 0.05;
            max-width: 10%;
        }
    }
</style>
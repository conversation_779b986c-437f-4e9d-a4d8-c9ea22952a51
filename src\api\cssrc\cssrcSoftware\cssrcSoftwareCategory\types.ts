export interface CssrcSoftwareCategoryVO extends BaseEntity {
  /**
   * 分类ID
   */
  categoryId: string | number;

  /**
   * 分类名称
   */
  categoryName: string;

  /**
   * 父级分类ID
   */
  parentId: string | number;

  /**
   * 子对象
   */
  children: CssrcSoftwareCategoryVO[];

  /**
   * 祖级分类路径
   */
  ancestors: string;

  /**
   * 排序
   */
  orderNum: number;

  /**
   * 所属范围
   */
  softwareRange: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

}

export interface CssrcSoftwareCategoryForm extends BaseEntity {
  /**
   * 分类ID
   */
  categoryId?: string | number;

  /**
   * 分类名称
   */
  categoryName?: string;

  /**
   * 父级分类ID
   */
  parentId?: string | number;

  /**
   * 祖级分类路径
   */
  ancestors?: string;

  /**
   * 排序
   */
  orderNum: number;

  /**
   * 所属范围
   */
  softwareRange?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

}

/**
 * 软件分类类型
 */
export interface CssrcSoftwareCategoryTreeVO extends BaseEntity {
  id: number | string;
  name: string;
  label: string;
  parentId: number | string;
  weight: number;
  children: CssrcSoftwareCategoryTreeVO[];
  disabled: boolean;
}

export interface CssrcSoftwareCategoryQuery {

  /**
   * 分类名称
   */
  categoryName?: string;

  /**
   * 父级分类ID
   */
  parentId?: string | number;

  /**
   * 祖级分类路径
   */
  ancestors?: string;

  /**
   * 所属范围
   */
  softwareRange?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}




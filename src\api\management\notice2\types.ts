export interface NoticeVO extends BaseEntity {
  noticeId: string | number;
  noticeTitle: string;
  noticeType: string;
  noticeContent: string;
  status: string;
  remark: string;
  createByName: string;
  createByNickName: string;
  ossIds: Array<string | number>;
  isTop: string;
  topDay: number;
  viewCount: number;
  isRead: boolean;
  downloadCount: number;
  noticeSource : string;
  flowStatus: string;
  secret: string;
  // imgOssIds: string;
}

export interface NoticeQuery extends PageQuery {
  noticeTitle: string;
  createByName: string;
  createByNickName: string;
  status: string;
  noticeType: string;
  flowStatus: string;
  secret: string;
}

export interface NoticeForm {
  noticeId: number | string | undefined;
  noticeTitle: string;
  noticeType: string;
  noticeContent: string;
  status: string;
  remark: string;
  createByName: string;
  createByNickName: string;
  ossIds: Array<string | number>;
  isTop: string;
  topDay: number;
  viewCount: number;
  isRead: boolean;
  downloadCount: number;
  noticeSource : string;
  flowStatus: string;
  secret: string;
  // imgOssIds: string;
}

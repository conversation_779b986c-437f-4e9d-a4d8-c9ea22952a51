import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CssrcSoftwareAccessVO, CssrcSoftwareAccessForm, CssrcSoftwareAccessQuery } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareAccess/types';

/**
 * 查询软件入网申请列表
 * @param query
 * @returns {*}
 */

export const listCssrcSoftwareAccess = (query?: CssrcSoftwareAccessQuery): AxiosPromise<CssrcSoftwareAccessVO[]> => {
  return request({
    url: '/cssrc/cssrcSoftwareAccess/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询软件入网申请详细
 * @param accessId
 */
export const getCssrcSoftwareAccess = (accessId: string | number): AxiosPromise<CssrcSoftwareAccessVO> => {
  return request({
    url: '/cssrc/cssrcSoftwareAccess/' + accessId,
    method: 'get'
  });
};

/**
 * 新增软件入网申请
 * @param data
 */
export const addCssrcSoftwareAccess = (data: CssrcSoftwareAccessForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareAccess',
    method: 'post',
    data: data
  });
};

/**
 * 修改软件入网申请
 * @param data
 */
export const updateCssrcSoftwareAccess = (data: CssrcSoftwareAccessForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareAccess',
    method: 'put',
    data: data
  });
};

/**
 * 删除软件入网申请
 * @param accessId
 */
export const delCssrcSoftwareAccess = (accessId: string | number | Array<string | number>) => {
  return request({
    url: '/cssrc/cssrcSoftwareAccess/' + accessId,
    method: 'delete'
  });
};

import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CssrcSoftwareCategoryVO, CssrcSoftwareCategoryForm, CssrcSoftwareCategoryQuery, CssrcSoftwareCategoryTreeVO } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/types';

/**
 * 查询软件分类列表
 * @param query
 * @returns {*}
 */

export const listCssrcSoftwareCategory = (query?: CssrcSoftwareCategoryQuery): AxiosPromise<CssrcSoftwareCategoryVO[]> => {
  return request({
    url: '/cssrc/cssrcSoftwareCategory/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询软件分类下拉树结构
 */
export const categoryTreeSelect = (): AxiosPromise<CssrcSoftwareCategoryTreeVO[]> => {
  return request({
    url: '/cssrc/cssrcSoftwareCategory/categoryTree',
    method: 'get'
  });
};

/**
 * 查询软件分类详细
 * @param categoryId
 */
export const getCssrcSoftwareCategory = (categoryId: string | number): AxiosPromise<CssrcSoftwareCategoryVO> => {
  return request({
    url: '/cssrc/cssrcSoftwareCategory/' + categoryId,
    method: 'get'
  });
};

/**
 * 新增软件分类
 * @param data
 */
export const addCssrcSoftwareCategory = (data: CssrcSoftwareCategoryForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareCategory',
    method: 'post',
    data: data
  });
};

/**
 * 修改软件分类
 * @param data
 */
export const updateCssrcSoftwareCategory = (data: CssrcSoftwareCategoryForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareCategory',
    method: 'put',
    data: data
  });
};

/**
 * 删除软件分类
 * @param categoryId
 */
export const delCssrcSoftwareCategory = (categoryId: string | number | Array<string | number>) => {
  return request({
    url: '/cssrc/cssrcSoftwareCategory/' + categoryId,
    method: 'delete'
  });
};

export default {
  categoryTreeSelect,
  getCssrcSoftwareCategory,
  addCssrcSoftwareCategory,
  updateCssrcSoftwareCategory,
  delCssrcSoftwareCategory
};
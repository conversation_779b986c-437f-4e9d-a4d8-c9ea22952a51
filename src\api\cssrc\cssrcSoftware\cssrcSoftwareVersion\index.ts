import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CssrcSoftwareVersionVO, CssrcSoftwareVersionForm, CssrcSoftwareVersionQuery } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareVersion/types';

/**
 * 查询软件版本详情列表
 * @param query
 * @returns {*}
 */

export const listCssrcSoftwareVersion = (query?: CssrcSoftwareVersionQuery): AxiosPromise<CssrcSoftwareVersionVO[]> => {
  return request({
    url: '/cssrc/cssrcSoftwareVersion/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询软件版本详情详细
 * @param versionId
 */
export const getCssrcSoftwareVersion = (versionId: string | number): AxiosPromise<CssrcSoftwareVersionVO> => {
  return request({
    url: '/cssrc/cssrcSoftwareVersion/' + versionId,
    method: 'get'
  });
};

/**
 * 新增软件版本详情
 * @param data
 */
export const addCssrcSoftwareVersion = (data: CssrcSoftwareVersionForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareVersion',
    method: 'post',
    data: data
  });
};

/**
 * 修改软件版本详情
 * @param data
 */
export const updateCssrcSoftwareVersion = (data: CssrcSoftwareVersionForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareVersion',
    method: 'put',
    data: data
  });
};

/**
 * 删除软件版本详情
 * @param versionId
 */
export const delCssrcSoftwareVersion = (versionId: string | number | Array<string | number>) => {
  return request({
    url: '/cssrc/cssrcSoftwareVersion/' + versionId,
    method: 'delete'
  });
};

import request from '@/utils/request'

// 获取软件列表
export function getSoftwareList(params: any) {
  return request({
    url: '/public/software/list',
    method: 'get',
    params
  })
}

// 获取软件详情
export function getSoftwareDetail(softwareId: number) {
  return request({
    url: `/public/software/${softwareId}`,
    method: 'get'
  })
}

// 获取软件版本列表
export function getSoftwareVersions(softwareId: number) {
  return request({
    url: `/public/software/${softwareId}/versions`,
    method: 'get'
  })
}

// 获取软件评论
export function getSoftwareComments(softwareId: number, params: any) {
  return request({
    url: `/public/software/${softwareId}/comments`,
    method: 'get',
    params
  })
}

// 获取版本评论
export function getVersionComments(versionId: number, params: any) {
  return request({
    url: `/public/software/${versionId}/comments`,
    method: 'get',
    params
  })
}

// 添加软件评论
export function addSoftwareComment(softwareId: number, data: any) {
  return request({
    url: `/public/software/${softwareId}/comments`,
    method: 'post',
    data
  })
}

// 添加版本评论
export function addVersionComment(versionId: number, data: any) {
  return request({
    url: `/public/software/${versionId}/comments`,
    method: 'post',
    data
  })
}

// 回复评论
export function replyComment(commentId: number, data: any) {
  return request({
    url: `/public/software/comments/${commentId}/reply`,
    method: 'post',
    data
  })
}

// 点赞/取消点赞评论
export function toggleCommentLike(commentId: number, likeType: number) {
  return request({
    url: `/public/software/comments/${commentId}/like`,
    method: 'post',
    params: { likeType }
  })
}

// 下载软件版本
export function downloadSoftwareVersion(versionId: number) {
  return request({
    url: `/public/software/download/${versionId}`,
    method: 'get'
  })
}

// 搜索软件
export function searchSoftware(params: any) {
  return request({
    url: '/public/software/search',
    method: 'get',
    params
  })
}

// 获取热门软件
export function getPopularSoftware(limit: number = 10) {
  return request({
    url: '/public/software/popular',
    method: 'get',
    params: { limit }
  })
}

// 获取软件统计
export function getSoftwareStatistics() {
  return request({
    url: '/public/software/statistics',
    method: 'get'
  })
}
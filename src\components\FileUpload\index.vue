<template>
  <div class="upload-file">
    <el-upload
      ref="fileUploadRef"
      :multiple="multiple"
      :auto-upload="false"
      :file-list="fileList"
      :limit="limit"
      :accept="fileAccept"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :show-file-list="false"
      :on-change="handleChange"
      :drag="false"
      :headers="headers"
      class="upload-file-uploader"
      v-if="!disabled"
    >
    <!-- :action="uploadFileUrl"
      :data="uploadData" 
    -->
    <!-- 选择文件 -->
    <template #trigger>
      <el-button type="primary" :disabled="disabled || (multiple === false && fileList.length > 0)">选取文件</el-button>
    </template>
    </el-upload>

    <!-- 上传提示 -->
    <div v-if="showTip && !disabled" class="el-upload__tip">
      请上传
      <!-- <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template> -->
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
      </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li v-for="(file, index) in fileList" :key="file.uid" class="el-upload-list__item ele-upload-list__item-content">
        <div class="el-icon-document">
          <span> {{ getTruncatedFileName(file.name) }} </span>
        </div>
        <div class="ele-upload-list__item-content-action">
          <div label="文件密级" class="ele-upload-list__item-content-action-item">密级：
            <el-select 
              v-model="file.fileSecret" 
              placeholder="请选择密级" 
              :class="{'is-error': !file.fileSecret}" 
              class="secret-select"
              :disabled="file.status === 'success'">
              <el-option v-for="dict in fileSecretOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </div>
          <div class="ele-upload-list__item-content-status">
            <el-progress v-if="file.status === 'uploading'" :percentage="file.progress" :text-inside="false" :stroke-width="7"  />
            <span v-else-if="file.status === 'waiting'">待上传</span>
            <span v-else-if="file.status === 'success'" style="color:var(--vxe-success-color)">上传成功</span>
            <span v-else-if="file.status === 'fail'" style="color:var(--vxe-danger-color)">上传失败</span>
          </div>
          <div style="width: 36px; margin-top: 0px;">
            <el-button type="danger" link @click="handleDelete(index)" :disabled="props.disabled">删除</el-button>
          </div>
        </div>
      </li>
    </transition-group>
    
    <!-- 上传按钮 -->
    <el-button type="primary" @click="uploadFiles" :disabled="fileList.length === 0 || props.disabled">上传文件</el-button>
  </div>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes';
import { delOss, listByIds } from '@/api/system/oss';
import { globalHeaders } from '@/utils/request';
import { ref, defineExpose } from 'vue';
import axios from 'axios';

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: () => []
  },
  // 数量限制
  limit: propTypes.number.def(5),
  // 大小限制(MB)
  // fileSize: propTypes.number.def(5),
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: propTypes.array.def(['doc', 'docx', 'xls', 'xlsx', 'ppt', 'txt', 'pdf', 'zip']),
  // 是否显示提示
  isShowTip: propTypes.bool.def(true),
  // 父组件传递密级过滤后的字典数据
  fileSecretOptions: {
    type: [Object, Array],
    requird: true
  },
  // 是否为编辑模式
  isEdit: propTypes.bool.def(false),
  // 是否禁用
  disabled: propTypes.bool.def(false),
  // 是否多选文件
  multiple:propTypes.bool.def(true),
  // 初始文件列表
  initialFiles: propTypes.array.def([]),
  // 父组件传递的 folderId，在 JavaScript 中，0 在布尔上下文中会被视为 false，因此修改下述验证器逻辑
  folderId: {
    type: [Number, String],
    required: true,
    validator: (value) => {
      return value !== null && value !== 'undefined'
    }
  }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
// const { sys_file_secret } = toRefs<any>(proxy?.useDict('sys_file_secret'));
const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void;
  (e: 'upload-success', fileInfo: { ossId: string; originalName: string; fileAll: any }): void;
  (e: 'file-remove', file: any): void;
  (e: 'immediate-submit'): void;
}>();
const number = ref(0);
const uploadList = ref<any[]>([]);

const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + '/resource/oss/upload'); // 上传文件服务器地址
const headers = ref(globalHeaders());

const fileList = ref<any[]>([]);
const showTip = computed(() => props.isShowTip && (props.fileType));

// 清空文件列表
const clearFileList = () => {
  fileList.value = [];
}
// 判断是否成功上传所有文件
const allFilesUploaded = computed(() => {
  return fileList.value.length > 0 && fileList.value.every(file => file.status === 'success');
});
defineExpose({
  clearFileList,
  allFilesUploaded,
  getFileList:() => fileList.value
})

const isValid = ref(false);
const isAllFileSecretSelected = computed(() => {
  return fileList.value.length > 0 && fileList.value.every(file => file.fileSecret !== undefined && file.fileSecret !== '');
});

watch(isAllFileSecretSelected, (newValue) => {
  isValid.value = newValue;
});

// 监听 fileType 变化，更新 fileAccept
const fileAccept = computed(() => props.fileType.map((type) => `.${type}`).join(','));

watch(
  () => props.modelValue,
  async (val) => {
    if (!val || (typeof val !== 'string' && !Array.isArray(val))) {
      fileList.value = [];
      return [];
    }
    let temp = 1;
    // 首先将值转为数组
    let list: any[] = [];
    if (Array.isArray(val)) {
      // 如果 val 是数组，遍历每个元素并调用 listByIds
      for (const id of val) {
        if (typeof id === 'string' || typeof id === 'number') {
          const res = await listByIds(id); // 逐个调用
          list.push(...res.data.map((oss) => ({
            name: oss.originalName,
            url: oss.url,
            ossId: oss.ossId
          })));
        }
      }
    } else {
      // 如果 val 是单个值，直接调用 listByIds
      if (typeof val === 'string' || typeof val === 'number') {
        const res = await listByIds(val);
        list = res.data.map((oss) => ({
          name: oss.originalName,
          url: oss.url,
          ossId: oss.ossId
        }));
      }
    }
    // 然后将数组转为对象数组
    fileList.value = list.map((item) => {
      // item = { name: item.name, url: item.url, ossId: item.ossId };
      // item.uid = item.uid || new Date().getTime() + temp++;
      // return item;

      // 检查 fileList 中是否已经存在该文件
      const existingFile = fileList.value.find(f => f.ossId === item.ossId);
      if (existingFile) {
        // 如果存在，保留现有状态
        return existingFile;
      } else {
        // 如果不存在，创建新文件对象
        return {
          name: item.name,
          url: item.url,
          ossId: item.ossId,
          uid: new Date().getTime() + temp++,
          fileSecret: '', // 初始化 fileSecret 为空字符串
          folderId: props.folderId !== undefined ? props.folderId : 0, // 初始化 folderId 为传入的 folderId 或默认的 0
          isTemp: false, // 标记为非临时文件
          status: 'success', // 标记为上传成功状态
          progress: 100 // 标记为上传完成
        };
      }
    });
  },
  { deep: true, immediate: true }
);

// 根据 isEdit 初始化 fileList
watch(
  () => props.isEdit,
  (newValue) => {
    if (newValue) {
      fileList.value = props.initialFiles;
    } else {
      fileList.value = [];
    }
  },
  { immediate: true }
);

watch(() => props.initialFiles, (newFiles) => {
  if (newFiles && newFiles.length > 0) {
    fileList.value = [...newFiles];
  }
}, { immediate: true, deep: true });

// 监听 folderId 变化，更新 fileList 中的 folderId
watch(() => props.folderId, (newVal) => {
  if (newVal !== undefined) {
    fileList.value = fileList.value.map(file => ({
      ...file,
      folderId: newVal
    }));
  }
}, { immediate: true });

// 选择文件进入等待上传列表
const handleChange = (file: UploadFile) => {
  if (!file.name) return false;
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1];
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy?.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join('/')}格式文件!`);
      return false;
    }
  }
  // 校检文件名是否包含特殊字符
  if (file.name.includes(',')) {
    proxy?.$modal.msgError('文件名不正确，不能包含英文逗号!');
    return false;
  }
  // 校检文件大小
  // if (props.fileSize) {
  //   const isLt = file.size / 1024 / 1024 < props.fileSize;
  //   if (!isLt) {
  //     proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
  //     return false;
  //   }
  // }

  // 确保 folderId 被正确设置
  const folderId = props.folderId !== undefined ? props.folderId : 0;

  fileList.value.push({
    file,
    name:file.name,
    url: file.url,
    fileSecret: '',
    folderId: folderId,  // 使用传入的 folderId 或默认的 0
    uid: new Date().getTime() + fileList.value.length + 1,
    ossId: '',
    isTemp: true, // 标记为临时文件
    status: 'waiting', // 初始状态：标记为等待上传状态
    progress: 0 // 初始上传进度为0
  });
};

// 文件个数超出
const handleExceed = () => {
  proxy?.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
};

// 上传失败
const handleUploadError = () => {
  proxy?.$modal.msgError('文件上传失败');
};

// 上传成功回调
const handleUploadSuccess = (res: any, file: UploadFile) => {
  if (res.code === 200) {
    file.status = 'success';
    proxy?.$modal.msgSuccess('上传成功');
  } else {
    file.status = 'fail';
    proxy?.$modal.msgError(res.msg);
  }
};

// 删除文件
const handleDelete = (index: number) => {
  proxy?.$modal.confirm('确认要删除该文件吗？删除后将立即生效，无法恢复。').then(async () => {
    const file = fileList.value[index];
    
    try {
      if (file.ossId) {
        // 对已上传的文件调用删除接口
        await delOss(file.ossId);
        
        // 立即从文件列表中移除
        fileList.value.splice(index, 1);
        
        // 立即更新父组件的值
        emit('update:modelValue', listToString(fileList.value));
        
        // 触发文件删除事件，通知父组件
        emit('file-remove', {
          ...file,
          action: 'delete' //标记为删除操作
        });
        
        // 如果父组件有提交逻辑，立即触发
        emit('immediate-submit');
        
        proxy?.$modal.msgSuccess('删除成功');
      } else {
        // 没有ossId，直接从列表中移除
        fileList.value.splice(index, 1);
        emit('update:modelValue', listToString(fileList.value));
        emit('file-remove', file);
        emit('immediate-submit');
        proxy?.$modal.msgSuccess('删除成功');
      }
    } catch (error) {
      proxy?.$modal.msgError('删除失败: '+ error);
    }
  }).catch(() => {
    // 用户取消删除操作
  });
}

// 上传结束处理
// const uploadedSuccessfully = () => {
//   if (number.value > 0 && uploadList.value.length === number.value) {
//     fileList.value = fileList.value.filter((f) => f.url !== undefined).concat(uploadList.value);
//     uploadList.value = [];
//     number.value = 0;
//     emit('update:modelValue', listToString(fileList.value));
//     proxy?.$modal.closeLoading();
//   }
// };

// 文件名称过长时处理
const getTruncatedFileName = (name: string): string => {
  const truncatedName = getFileName(name);
  return truncatedName.length > 30 ? `${truncatedName.slice(0, 26)}...${truncatedName.slice(-4)}` : truncatedName;
};
// 获取文件名称
const getFileName = (name: string) => {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf('/') > -1) {
    return name.slice(name.lastIndexOf('/') + 1);
  } else {
    return name;
  }
};

// 对象转成指定字符串分隔
const listToString = (list: any[], separator?: string) => {
  let strs = '';
  separator = separator || ',';
  list.forEach((item) => {
    if (item.ossId) {
      strs += item.ossId + separator;
    }
  });
  return strs != '' ? strs.substring(0, strs.length - 1) : '';
};
// 上传文件
const uploadFiles = () => {
  if (!isValid.value) {
    proxy?.$modal.msgError('请先为所有文件选择密级');
    return;
  }
  fileList.value.forEach(async (file) => {
    if (file.status === 'waiting') {
      file.status = 'uploading'; // 更新文件状态为上传中

      const formData = new FormData();
      formData.append('file', file.file.raw);
      formData.append('fileSecret', file.fileSecret);
      formData.append('folderId', file.folderId);
      console.log('file.folderId:', file.folderId);

      // 使用 axios 或其他 HTTP 客户端发送请求
      axios.post(uploadFileUrl.value, formData, {
          headers: {
            ...headers.value,
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            file.progress = percentCompleted; // 更新文件上传进度
          }
      })
      .then((res) => {
        if (res.data.code === 200) {
          file.status = 'success';
          file.ossId = res.data.data.ossId;
          // 更新文件列表
          const updatedFile = {
            originalName: file.name,
            url: res.data.data.url,
            ossId: res.data.data.ossId,
            fileSecret: file.fileSecret,
            folderId: file.folderId,
            uid: file.uid,
            status: 'success',
            progress: 100
          };
          // 发送上传成功事件给父组件
          emit('upload-success', {
            ossId: res.data.data.ossId, 
            originalName: file.file.name,
            fileAll: updatedFile
          });

          proxy?.$modal.msgSuccess('上传成功');
          file.ossId = res.data.data.ossId;
        } else {
          file.status = 'fail';
          proxy?.$modal.msgError(res.data.msg);
        }
      })
      .catch(() => {
        file.status = 'fail'; // 更新文件状态为上传失败
        proxy?.$modal.msgError('上传文件失败');
      })
    }
  });
};
</script>

<style scoped lang="scss">
.upload-file {
  width: 650px;
}
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 3;
  margin-bottom: 10px;
  position: relative;
}
.el-icon-document {
  max-width: 284px;
  line-height: 20px;
  overflow: hidden;
  cursor: default;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 340px;
  height: 42px;
  font-size: 14px;
}
.ele-upload-list__item-content-action span {
  width: 60px;
  margin-left: 15px;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 2px;
}
.ele-upload-list__item-content-action-item {
  color: #606266;
  font-size: 14px;
}
.secret-select {
  width: 140px;
}
.is-error { 
  :deep(.el-select__wrapper) {
    box-shadow: 0 0 0 1px var(--el-color-danger-light-3) inset;
    background-color: var(--el-color-danger-light-9);
  }
}
.ele-upload-list__item-content-status {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 10px;
  margin-right: 8px;
  width: 90px;
  height: 14px;
}
.ele-upload-list__item-content-status .el-progress--line {
  max-width: 80px;
}
</style>

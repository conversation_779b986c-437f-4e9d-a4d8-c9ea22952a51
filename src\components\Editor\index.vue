
<template>
    <div class="editor">
        <!-- 工具栏 -->
        <Toolbar
            style="border-bottom: 1px solid #ccc"
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            :mode="mode"
        />
        <!-- 编辑器 -->
        <Editor
            :style= "{height: height + 'px'}"
            :defaultConfig="editorConfig"
            v-model="valueHtml"
            :mode="mode"
            :read-only = "readOnly"
            @onChange = "handleChange"
            @onCreated = "handleCreated"
        />
        <!-- 图片密级选择弹窗 -->
        <el-dialog
          v-model="showSecurityDialog"
          title="选择图片"
          width="500px"
          append-to-body
        >
          <el-form :model="imageInfo" label-width="100px" ref="imageFormRef" :rules="imageFormRules">
            <el-form-item label="图片预览">
              <img :src="imageInfo.tempUrl" style="max-width: 100%; max-height: 200px" />
            </el-form-item>
            <el-form-item label="宽度">
              <el-input-number v-model="imageInfo.width" :min="1" :max="2000" :disabled="imageInfo.maintainAspectRatio && imageInfo.aspectRatio === 0"
            @change="onWidthChange"/>
              <el-text style="margin-left: 6px;">px</el-text>
            </el-form-item>
            <el-form-item label="高度">
              <el-input-number v-model="imageInfo.height" :min="1" :max="2000" :disabled="imageInfo.maintainAspectRatio && imageInfo.aspectRatio === 0"
            @change="onHeightChange"/>
              <el-text style="margin-left: 6px;">px</el-text>
            </el-form-item>
            <el-form-item label="纵横比">
              <el-checkbox 
                v-model="imageInfo.maintainAspectRatio" 
                @change="onAspectRatioChange"
                label="保持纵横比"
                size="large"
              />
            </el-form-item>
            <el-form-item label="密级" prop="fileSecret">
              <el-select v-model="imageInfo.fileSecret" placeholder="请选择密级" class="secret-select">
                <el-option v-for="dict in filteredSysFileSecret" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button @click="showSecurityDialog = false">取 消</el-button>
            <el-button type="primary" @click="confirmImageSettings">确 定</el-button>
          </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'
import { onBeforeUnmount, ref, shallowRef, watch, nextTick } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { globalHeaders } from '@/utils/request'

type ImageElement = { src: string; url: string; href: string }

const props = defineProps({
  modelValue: {
    type: String,
    default: '请输入内容...'
  },
  height: {
    type: Number,
    default: 400
  },
  readOnly: {
    type: Boolean,
    default: false
  }
})

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const emit = defineEmits(['update:modelValue']);

const editorRef = shallowRef();
const valueHtml = ref(props.modelValue);
const mode = ref('default');

// 表单引用和验证规则
const imageFormRef = ref();
const imageFormRules = {
  fileSecret: [
    { required: true, message: '请选择图片密级', trigger: 'change' }
  ]
}

// 密级选择弹窗相关
const showSecurityDialog = ref(false);
const imageInfo = reactive({
  tempUrl: '',
  width: 300,
  height: 200,
  originalWidth: 0, // 原始宽度
  originalHeight: 0, // 原始高度
  aspectRatio: 0, // 纵横比
  maintainAspectRatio: true, // 是否保持纵横比
  fileSecret: '',
  insertFn: null as any,
  file: null as File | null // 保存原始文件
});

const { sys_file_secret } = toRefs<any>(proxy?.useDict('sys_file_secret'));
// 过滤 sys_file_secret 字典数据
const filteredSysFileSecret = computed(() => {
    return sys_file_secret.value.filter((dict: any) => dict.value === '0' || dict.value === '5');
});

// 编辑器创建完成时的回调
const handleCreated = (editor: any) => {
  editorRef.value = editor;
  props.readOnly ? editor.disable() : editor.enable()
  nextTick(() => {
    if (props.modelValue && editor) {
      try {
        editor.setHtml(props.modelValue) // 确保第一次设置时内容被正确渲染
      } catch (error) {
        console.warn('设置编辑器内容失败:', error)
      }
    }
  })
}


// 内容变化时的回调
const handleChange = (editor: any) => {
  // 检查编辑器实例是否存在
  if (!editor || !editorRef.value) {
    console.warn('编辑器实例不存在，跳过内容更新');
    return
  }
  try {
    // 将编辑器内容更新到 v-model
    emit('update:modelValue', editor.getHtml());
  } catch (error) {
    console.warn('获取编辑器内容失败:', error);
  }
}

// 监听父组件传入的内容变化
watch(
  () => props.modelValue, 
  (val) => {
    if (val !== valueHtml.value) {
      valueHtml.value = val || '<p></p>'
      // 如果编辑器已经创建，更新编辑器内容
      if (editorRef.value) {
        try {
          editorRef.value.setHtml(valueHtml.value);
        } catch (error) {
          console.warn('更新编辑器内容失败:', error)
        }
      }
    }
  }, 
  { immediate: true }
)

watch(
  () => props.readOnly,
  (newVal) => {
    editorConfig.readOnly = newVal
    if (editorRef.value) {
      // 通过API强制设置只读状态
      newVal ? editorRef.value.disable() : editorRef.value.enable()
    }
})

// 图片上传成功返回图片地址
// const handleUploadSuccess = (res: any) => {
//   // 如果上传成功
//   if (res.code === 200) {
//     // 获取富文本实例
//     const quill = toRaw(quillEditorRef.value).getQuill();
//     // 获取光标位置
//     const length = quill.selection.savedRange.index;
//     // 插入图片，res为服务器返回的图片链接地址
//     quill.insertEmbed(length, 'image', res.data.url);
//     // 调整光标到最后
//     quill.setSelection(length + 1);
//     proxy?.$modal.closeLoading();
//   } else {
//     proxy?.$modal.msgError('图片插入失败');
//     proxy?.$modal.closeLoading();
//   }
// };

// // 图片上传前拦截
// const handleBeforeUpload = (file: any) => {
//   const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg'];
//   const isJPG = type.includes(file.type);
//   //检验文件格式
//   if (!isJPG) {
//     proxy?.$modal.msgError(`图片格式错误!`);
//     return false;
//   }
//   // 校检文件大小
//   if (props.fileSize) {
//     const isLt = file.size / 1024 / 1024 < props.fileSize;
//     if (!isLt) {
//       proxy?.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
//       return false;
//     }
//   }
// };

// 确认图片密级设置并上传图片
const confirmImageSettings = async () => {
  if (!imageInfo.file || !imageInfo.insertFn) {
    proxy?.$modal.msgError('图片信息不完整');
    return;
  }
  // 表单验证
  const valid = await imageFormRef.value.validate();
  if (!valid) {
    proxy?.$modal.msgError('请选择图片密级');
    return false;
  }
  try {
    // 准备上传数据
    const formData = new FormData();
    formData.append('file', imageInfo.file);
    formData.append('fileSecret', imageInfo.fileSecret);
    
    // 添加其他需要的参数
    formData.append('width', imageInfo.width.toString());
    formData.append('height', imageInfo.height.toString());

    // 执行上传
    const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload';
    const response = await fetch(uploadUrl, {
      method: 'POST',
      headers: globalHeaders(),
      body: formData
    });

    const result = await response.json();
    
    if (result.code === 200) {
      // 使用服务器返回的URL插入图片到编辑器
      imageInfo.insertFn(result.data.url, { 
        width: imageInfo.width + 'px', 
        height: imageInfo.height + 'px',
        fileSecret: imageInfo.fileSecret
      });
      showSecurityDialog.value = false;
    } else {
      proxy?.$modal.msgError(result.msg || '图片上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    proxy?.$modal.msgError('图片上传失败');
  }
};

// 工具栏配置
const toolbarConfig = {
  // 移除菜单栏中不需要的功能
  excludeKeys:[
    'group-video',
    'insertVideo',
    'uploadVideo',
    'editVideoSize',
    'insertImage',
    'emotion'
  ]
}
// 编辑器改成动态配置reactive
const editorConfig = reactive({
  placeholder: '请输入内容...',
  readOnly: props.readOnly,
  MENU_CONF: {
    // 配置图片上传地址
    uploadImage: {
      server: import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload',
      headers: globalHeaders(),
      maxFileSize: 10 * 1024 * 1024, //去除上传图片大小限制
      fieldName:'file', // 上传图片的字段名必须设置为file
      maxNumberOfFiles: 5, // 限制最多上传 张图片
      allowedFileTypes: ['image/*'], // 限制上传文件类型
      // 自定义上传参数
      meta: {
        token: '',
        otherKey: '',
        fileSecret: '' // 为图片设置默认密级
      },
      metaWithUrl: false, // 自定义上传参数设置为 true，meta 信息包含在返回的 resp 对象中
      withCredentials: true, // 跨域请求时是否携带 cookie
      // 自定义插入图片
      customUpload: (file: File, insertFn: Function) => {
        // 重置图片信息，确保每次上传都清空之前的选择
        resetImageInfo();
        imageInfo.file = file; // 保存文件和插入函数
        imageInfo.insertFn = insertFn; // 保存插入函数，稍后在弹窗确认时调用
        imageInfo.tempUrl = URL.createObjectURL(file); // 创建临时URL用于预览
        const img = new Image(); // 设置默认尺寸
        img.onload = () => {
          // 保存原始尺寸和纵横比
          imageInfo.originalWidth = img.width;
          imageInfo.originalHeight = img.height;
          imageInfo.aspectRatio = img.width / img.height;

          // 设置默认尺寸为图片原始尺寸，但限制最大宽度
          const maxWidth = 1000;
          if (img.width > maxWidth) {
            imageInfo.width = maxWidth;
            imageInfo.height = Math.round((img.height * maxWidth) / img.width);
          } else {
            imageInfo.width = img.width;
            imageInfo.height = img.height;
          }
          // 默认勾选保持纵横比
          imageInfo.maintainAspectRatio = true;
          
          // 显示密级选择弹窗
          showSecurityDialog.value = true;
        }
        img.src = imageInfo.tempUrl;
      },
    },
    // 图片插入后的回调
    insertImage: {
      onInsertedImage(imageNode: ImageElement | null) {
        if (imageNode == null) return
      }
    }
  }
})

// 新增：重置图片信息的函数
const resetImageInfo = () => {
  imageInfo.tempUrl = '';
  imageInfo.width = 300;
  imageInfo.height = 200;
  imageInfo.originalWidth = 0;
  imageInfo.originalHeight = 0;
  imageInfo.aspectRatio = 0;
  imageInfo.maintainAspectRatio = true;
  imageInfo.fileSecret = '';
  imageInfo.insertFn = null;
  imageInfo.file = null;
};

// 宽度改变时的处理函数
const onWidthChange = (newWidth: number) => {
  if (imageInfo.maintainAspectRatio && imageInfo.aspectRatio > 0) {
    // 根据纵横比自动计算高度
    imageInfo.height = Math.round(newWidth / imageInfo.aspectRatio)
  }
}

// 高度改变时的处理函数
const onHeightChange = (newHeight: number) => {
  if (imageInfo.maintainAspectRatio && imageInfo.aspectRatio > 0) {
    // 根据纵横比自动计算宽度
    imageInfo.width = Math.round(newHeight * imageInfo.aspectRatio)
  }
}

// 纵横比选项改变时的处理函数
const onAspectRatioChange = (checked: boolean) => {
  if (checked && imageInfo.aspectRatio > 0) {
    // 启用保持纵横比时，以宽度为准重新计算高度
    imageInfo.height = Math.round(imageInfo.width / imageInfo.aspectRatio)
  }
}

// 添加清理临时URL的方法
const cleanupObjectUrl = () => {
  if (imageInfo.tempUrl) {
    URL.revokeObjectURL(imageInfo.tempUrl)
    imageInfo.tempUrl = ''
  }
}

// 监听弹窗关闭事件，清理临时URL
watch(showSecurityDialog, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时清理临时URL
    nextTick(() => {
      cleanupObjectUrl()
    })
  }
})

// 组件销毁前清理资源
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
    try {editor.destroy()} catch (e) {
      console.error('Error during editor destroy', e);
    }
  // 组件销毁前清理临时URL
  cleanupObjectUrl()
})

const content = ref('');
watch(
  () => props.modelValue,
  (v: string) => {
    if (v !== content.value) {
      content.value = v || '<p></p>';
    }
  },
  { immediate: true }
);

</script>

<style>
.editor {
  border: 1px solid #ccc;
  z-index: 100;
}
.editor img {
  max-width: 100%;
  height: auto;
}
</style>

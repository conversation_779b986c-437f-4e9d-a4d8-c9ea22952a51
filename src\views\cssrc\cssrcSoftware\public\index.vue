<template>
  <div class="software-library">
    <el-container>
      <!-- 左侧分类树 -->
      <el-aside width="300px" class="category-aside">
        <el-card shadow="hover" class="category-card">
          <template #header>
            <div class="card-header">
              <el-icon><FolderOpened /></el-icon>
              <span>软件分类</span>
            </div>
          </template>
          <el-input
            v-model="categoryFilter"
            placeholder="搜索分类"
            prefix-icon="Search"
            clearable
            class="category-search"
          />
          <el-tree
            ref="categoryTreeRef"
            :data="categoryTree"
            :props="{ label: 'label', children: 'children' }"
            node-key="id"
            :filter-node-method="filterCategoryNode"
            highlight-current
            :expand-on-click-node="false"
            :default-expanded-keys="defaultExpandedKeys"
            default-expand-all
            @node-click="handleCategoryClick"
            class="category-tree"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <el-icon class="tree-icon">
                  <Folder v-if="data.children && data.children.length > 0" />
                  <Document v-else />
                </el-icon>
                <span class="tree-label">{{ node.label }}</span>
                <span class="tree-count" v-if="data.softwareCount > 0">({{ data.softwareCount }})</span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </el-aside>

      <el-main class="main-content">
        <!-- 搜索和筛选区域 -->
        <el-card shadow="never" class="search-card">
          <template #header>
            <!-- 面包屑导航 -->
            <el-breadcrumb :separator-icon="ArrowRight" class="breadcrumb-nav">
              <el-breadcrumb-item>
                <el-text><el-icon><House /></el-icon>
                  软件库</el-text>
              </el-breadcrumb-item>
              <!-- <el-breadcrumb-item :to="{ path: '/' }">homepage</el-breadcrumb-item> -->
              <el-breadcrumb-item 
                v-for="item in breadcrumbItems" 
                :key="item.id"
                @click="handleBreadcrumbClick(item)"
                class="breadcrumb-clickable"
              >
                <a href="/" class="breadcrumb-a">{{ item.label }}</a>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </template>
          <el-form :model="searchForm" :inline="true" class="search-form">
            <el-form-item label="软件名称">
              <el-input
                v-model="searchForm.softwareName"
                placeholder="请输入软件名称"
                clearable
                @keyup.enter="handleSearch"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="排序方式">
              <el-select v-model="searchForm.sortBy" placeholder="选择排序方式" style="width: 150px">
                <el-option label="按名称" value="name" />
                <el-option label="按下载量" value="download" />
                <el-option label="按更新时间" value="time" />
              </el-select>
            </el-form-item>
            <el-form-item label="显示方式">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button value="grid">
                  <el-icon><Grid /></el-icon>
                  网格
                </el-radio-button>
                <el-radio-button value="list">
                  <el-icon><List /></el-icon>
                  列表
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
              <el-button icon="Refresh" @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 软件列表 -->
        <el-card shadow="never" class="software-list-card">
          <template #header>
            <el-row class="list-header">
              <el-col class="list-title">
                {{ currentCategory ? currentCategory.label : '全部软件' }}
                <el-tag size="small" type="info" v-if="total > 0">{{ total }}个软件</el-tag>
              </el-col>
            </el-row>
          </template>
          <el-row v-loading="loading" class="software-container">
            <!-- 网格视图 -->
            <el-row v-if="viewMode === 'grid'" class="software-grid">
              <el-row
                v-for="software in softwareList"
                :key="software.softwareId"
                class="software-card"
                @click="handleSoftwareClick(software)"
              >
                <div class="software-header-section">
                  <el-row class="software-icon">
                    <el-col :span="24">
                      <el-icon size="48"><Box /></el-icon>
                    </el-col>
                  </el-row>
                  
                  <el-row class="software-info">
                    <el-col :span="24" class="software-name" :title="software.softwareName">
                      {{ software.softwareName }}
                    </el-col>
                  </el-row>
                  
                  <el-row class="software-meta-row">
                    <el-col :span="8" v-if="software.manufacturer">
                      <el-text class="mx-1" type="info" size="default">
                        <el-icon><OfficeBuilding /></el-icon>
                        {{ software.manufacturer }}
                      </el-text>
                    </el-col>
                    <el-col :span="8" v-if="software.country">
                      <el-text class="mx-1" type="info" size="default">
                        <el-icon><Location /></el-icon>
                        {{ software.country }}
                      </el-text>
                    </el-col>
                    <el-col :span="8">
                      <el-text class="mx-1" type="info" size="default">
                        <el-icon><Download /></el-icon>
                        {{ software.downloadCount || 0 }}次下载
                      </el-text>
                    </el-col>
                  </el-row>
                </div>
                <el-divider />
                <div class="software-intro-section" v-if="software.intro">
                  <div class="software-intro" :title="software.intro">
                    {{ software.intro }}
                  </div>
                </div>
                <div class="software-actions">
                  <el-button type="primary" size="default" @click.stop="handleSoftwareClick(software)">
                    查看详情
                  </el-button>
                </div>
              </el-row>
            </el-row>

            <!-- 列表视图 -->
            <el-row v-else class="software-list">
              <el-table :data="softwareList" stripe style="width: 100%">
                <el-table-column prop="softwareName" label="软件名称" min-width="200">
                  <template #default="{ row }">
                    <div class="software-name-cell">
                      <el-icon class="software-icon-small"><Box /></el-icon>
                      <span class="software-name-text">{{ row.softwareName }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="manufacturer" label="厂商" width="150" />
                <el-table-column prop="country" label="国别" width="100" />
                <el-table-column prop="intro" label="简介" min-width="200" show-overflow-tooltip />
                <el-table-column prop="downloadCount" label="下载次数" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag size="small">{{ row.downloadCount || 0 }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" align="center">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="handleSoftwareClick(row)">
                      查看详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>

            <!-- 空状态 -->
            <div v-if="!loading && softwareList.length === 0" class="empty-state">
              <el-empty description="暂无软件数据" align="center">
                <el-button type="primary" @click="handleReset">重新加载</el-button>
              </el-empty>
            </div>
          </el-row>

          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="total > 0">
            <el-pagination
              v-model:current-page="queryParams.pageNum"
              v-model:page-size="queryParams.pageSize"
              :page-sizes="[12, 24, 48, 96]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </el-main>
    </el-container>

    <!-- 软件详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="detailDialog.software?.softwareName"
      width="90%"
      top="5vh"
      class="software-detail-dialog"
      :before-close="handleDialogClose"
    >
      <SoftwareDetail
        v-if="detailDialog.visible"
        :software="detailDialog.software"
        @close="detailDialog.visible = false"
        @download-success="handleDownloadSuccess"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElTree, ElMessage } from 'element-plus'
import { 
  Box, Download, Search, Refresh, FolderOpened, Folder, Document, 
  House, Grid, List, OfficeBuilding, Location 
} from '@element-plus/icons-vue'
import SoftwareDetail from '../components/SoftwareDetail.vue'
import { getSoftwareList } from '@/api/cssrc/cssrcSoftware/public'
import { categoryTreeSelect } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareCategory/index'
import { ArrowRight } from '@element-plus/icons-vue'

// 响应式数据
const categoryTreeRef = ref<InstanceType<typeof ElTree>>()
const categoryFilter = ref('')
const categoryTree = ref([])
const currentCategory = ref<any>(null)
const defaultExpandedKeys = ref([])
const loading = ref(false)
const softwareList = ref([])
const total = ref(0)
const viewMode = ref('grid') // 'grid' | 'list'

// 搜索表单
const searchForm = reactive({
  softwareName: '',
  sortBy: 'name'
})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 12,
  categoryId: null as number | null,
  softwareName: ''
})

// 详情对话框
const detailDialog = reactive({
  visible: false,
  software: null as any
})

// 面包屑导航数据
const breadcrumbItems = computed(() => {
  if (!currentCategory.value) return []
  
  const items = []
  let current = currentCategory.value
  
  // 构建面包屑路径
  while (current) {
    items.unshift({
      id: current.id,
      label: current.label
    })
    current = findParentCategory(current.id)
  }
  
  return items
})

// 监听分类过滤
watch(categoryFilter, (val) => {
  categoryTreeRef.value?.filter(val)
})

// 过滤分类节点
const filterCategoryNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.includes(value)
}

// 获取分类树
const getCategoryTree = async () => {
  try {
    const { data } = await categoryTreeSelect()
    categoryTree.value = data
    
    // 设置默认展开的节点
    if (data.length > 0) {
      defaultExpandedKeys.value = [data[0].id]
    }
  } catch (error) {
    console.error('获取分类树失败:', error)
    ElMessage.error('获取分类树失败')
  }
}

// 查找父级分类
const findParentCategory = (categoryId: number): any => {
  const findInTree = (nodes: any[], targetId: number, parent: any = null): any => {
    for (const node of nodes) {
      if (node.children) {
        for (const child of node.children) {
          if (child.id === targetId) {
            return node
          }
        }
        const found = findInTree(node.children, targetId, node)
        if (found) return found
      }
    }
    return null
  }
  
  return findInTree(categoryTree.value, categoryId)
}

// 查找分类节点
const findCategoryById = (categoryId: number): any => {
  const findInTree = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === categoryId) {
        return node
      }
      if (node.children) {
        const found = findInTree(node.children)
        if (found) return found
      }
    }
    return null
  }
  
  return findInTree(categoryTree.value)
}

// 获取软件列表
const getSoftwareListData = async () => {
  loading.value = true
  try {
    const { rows, total: totalCount } = await getSoftwareList(queryParams)
    
    // 根据排序方式处理数据
    let sortedRows = [...rows]
    switch (searchForm.sortBy) {
      case 'download':
        sortedRows.sort((a, b) => (b.downloadCount || 0) - (a.downloadCount || 0))
        break
      case 'time':
        sortedRows.sort((a, b) => new Date(b.updateTime || b.createTime).getTime() - new Date(a.updateTime || a.createTime).getTime())
        break
      case 'name':
      default:
        sortedRows.sort((a, b) => a.softwareName.localeCompare(b.softwareName))
        break
    }
    
    softwareList.value = sortedRows
    total.value = totalCount
  } catch (error) {
    console.error('获取软件列表失败:', error)
    ElMessage.error('获取软件列表失败')
  } finally {
    loading.value = false
  }
}

// 处理分类点击
const handleCategoryClick = (data: any) => {
  currentCategory.value = data
  queryParams.categoryId = data.id
  queryParams.pageNum = 1
  getSoftwareListData()
}

// 处理面包屑点击
const handleBreadcrumbClick = (item: any) => {
  const category = findCategoryById(item.id)
  if (category) {
    currentCategory.value = category
    queryParams.categoryId = item.id
    queryParams.pageNum = 1
    categoryTreeRef.value?.setCurrentKey(item.id)
    getSoftwareListData()
  }
}

// 处理搜索
const handleSearch = () => {
  queryParams.softwareName = searchForm.softwareName
  queryParams.pageNum = 1
  getSoftwareListData()
}

// 处理重置
const handleReset = () => {
  searchForm.softwareName = ''
  searchForm.sortBy = 'name'
  queryParams.softwareName = ''
  queryParams.categoryId = null
  queryParams.pageNum = 1
  currentCategory.value = null
  categoryTreeRef.value?.setCurrentKey(null)
  getSoftwareListData()
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1
  getSoftwareListData()
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  getSoftwareListData()
}

// 处理软件点击
const handleSoftwareClick = (software: any) => {
  detailDialog.software = software
  detailDialog.visible = true
}

// 处理对话框关闭
const handleDialogClose = () => {
  detailDialog.visible = false
}

// 处理下载成功
const handleDownloadSuccess = (software: any) => {
  // 更新软件列表中的下载次数
  const index = softwareList.value.findIndex(item => item.softwareId === software.softwareId)
  if (index !== -1) {
    softwareList.value[index].downloadCount = (softwareList.value[index].downloadCount || 0) + 1
  }
}

// 初始化
onMounted(() => {
  getCategoryTree()
  getSoftwareListData()
})
</script>

<style scoped lang="scss">
.software-library {
  height: 100vh;
  background-color: #f5f7fa;

  .el-container {
    height: 100%;
  }

  .category-aside {
    background-color: #f5f7fa;
    padding: 16px;

    .category-card {
      height: 100%;

      .card-header {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 16px;
        color: #303133;

        .el-icon {
          margin-right: 8px;
          color: #409eff;
        }
      }

      .category-search {
        margin-bottom: 16px;
      }

      .category-tree {
        max-height: calc(100vh - 200px);
        overflow-y: auto;

        .tree-node {
          display: flex;
          align-items: center;
          width: 100%;
          padding: 4px 0;

          .tree-icon {
            margin-right: 6px;
            color: #606266;
          }

          .tree-label {
            flex: 1;
            font-size: 14px;
          }

          .tree-count {
            font-size: 12px;
            color: #909399;
            margin-left: 8px;
          }
        }

        :deep(.el-tree-node__content) {
          height: 36px;
          
          &:hover {
            background-color: #f5f7fa;
          }
        }

        :deep(.el-tree-node.is-current > .el-tree-node__content) {
          background-color: #e6f7ff;
          color: #1890ff;
        }
      }
    }
  }

  .main-content {
    padding: 16px 16px 16px 0;
    overflow-y: auto;

    .breadcrumb-nav {
      .breadcrumb-clickable {
        cursor: pointer;
        &:hover {
          color: #409eff;
        }
      }
    }

    .search-card {
      margin-bottom: 16px;

      .search-form {
        margin: 0;

        .el-form-item {
          margin-bottom: 0;
        }
      }
    }

    .software-list-card {
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .list-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;
          color: #303133;

          .el-tag {
            margin-left: 8px;
          }
        }
      }

      .software-container {
        min-height: 400px;
        max-height: calc(100vh - 280px); // 确保整个容器不超出视窗
        overflow: hidden; // 隐藏容器溢出
      }

      // 网格视图样式
      .software-grid {
        display: flex;
        flex-direction: row;
        gap: 20px;
        // max-height: calc(100vh - 300px);
        padding: 10px 0;
        
        /* 隐藏滚动条 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari and Opera */
        }
        
        .software-card {
          display: flex;
          flex-direction: column;
          padding: 20px;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          background-color: #fff;
          cursor: pointer;
          transition: all 0.3s ease;
          width: 310px;
          height: 280px;
          margin-bottom: 0;

          &:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
            border-color: #409eff;
          }

          // 固定分隔线上方元素的高度
          .software-header-section {
            height: 120px; // 固定高度
            display: flex;
            flex-direction: column;
            flex-shrink: 0;

            .software-icon {
              text-align: center;
              margin-bottom: 12px;
              color: #409eff;
            }

            .software-info {
              .software-name {
                font-size: 20px;
                font-weight: 600;
                color: #303133;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-bottom: 8px;
              }
            }

            .software-meta-row {
              text-align: center;
              margin-top: auto; // 推到底部
            }
          }

          // 软件简介区域
          .software-intro-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            margin-bottom: 12px;

            .software-intro {
              font-size: 13px;
              color: #606266;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 3; // 限制文本为三行
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              flex: 1;
            }
          }

          // 查看详情按钮区域
          .software-actions {
            text-align: center;
            flex-shrink: 0;
            margin-top: auto;
          }
        }
      }

      // 列表视图样式
      .software-list {
        width: 100%;
        .software-name-cell {
          display: flex;
          align-items: center;

          .software-icon-small {
            margin-right: 8px;
            color: #409eff;
          }

          .software-name-text {
            font-weight: 500;
          }
        }

        :deep(.el-table__row) {
          cursor: pointer;

          &:hover {
            background-color: #f5f7fa;
          }
        }
      }

      .empty-state {
        padding: 60px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
      }

      .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px solid #e4e7ed;
      }
    }
  }
}

.software-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }

  :deep(.el-dialog__header) {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e4e7ed;
  }
}
.breadcrumb-a {
  color:#606266;
}
.breadcrumb-a :hover{
  color:#409eff;
}

// 响应式设计
@media (max-width: 768px) {
  .software-library {
    .category-aside {
      width: 100% !important;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1000;
      height: 100vh;
      transform: translateX(-100%);
      transition: transform 0.3s ease;

      &.mobile-open {
        transform: translateX(0);
      }
    }

    .main-content {
      padding: 12px;

      .software-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        .software-card {
          height: auto;
          min-height: 160px;
        }
      }
    }
  }
}
:deep(.el-divider--horizontal) {
  margin: 10px 0;
  padding: 0 0;
}
</style>




export interface LoginInfoVO {
  infoId: string | number;
  tenantId: string | number;
  userName: string;
  secret: string;
  status: string;
  ipaddr: string;
  loginLocation: string;
  browser: string;
  os: string;
  msg: string;
  loginTime: string;
}

export interface LoginInfoQuery extends PageQuery {
  ipaddr: string;
  userName: string;
  secret: string;
  status: string;
  orderByColumn: string;
  isAsc: string;
}

<!-- 添加或修改公告对话框 -->
<template>
<div>
<div class="p-2">
    <el-card shadow="never" style="border: 0px;">
        <approvalButton
            @submitForm="submitForm"
            @approvalVerifyOpen="approvalVerifyOpen"
            @handleApprovalRecord="handleApprovalRecord"
            :buttonLoading="buttonLoading"
            :id="form.noticeId"
            :status="form.flowStatus"
            :pageType="routeParams.type"
        />
    </el-card>
    <el-card shadow="never" style="height: 82vh; border: 0px; overflow-y: auto">
        <el-form ref="noticeFormRef" v-loading="loading" :disabled="routeParams.type === 'view' || routeParams.type === 'approval'" :model="form" :rules="rules" label-width="80px">
        <el-row>
            <el-col :span="10">
            <el-form-item label="公告标题" prop="noticeTitle">
                <el-input v-model="form.noticeTitle" placeholder="请输入公告标题" />
            </el-form-item>
            </el-col>
            <el-col :span="10">
            <el-form-item label="公告类型" prop="noticeType">
                <el-select v-model="form.noticeType" placeholder="请选择">
                    <el-option v-for="dict in sys_notice_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            </el-col>
            <el-col :span="10">
            <el-form-item label="公告密级" prop="secret">
                <el-select v-model="form.secret" placeholder="请选择公告密级">
                    <el-option v-for="dict in filteredSysFileSecret" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                </el-select>
            </el-form-item>
            </el-col>
            <el-col :span="10">
            <el-form-item label="来源">
                <el-input v-model="form.noticeSource"/>
            </el-form-item>
            </el-col>
            <el-col :span="10">
            <el-form-item label="是否置顶" prop="isTop">
                <el-radio-group v-model="form.isTop">
                <el-radio v-for="dict in sys_notice_istop" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                </el-radio-group>
            </el-form-item>
            </el-col>
            <el-col :span="10">
            <el-form-item label="置顶天数" prop="topDay">
                <el-input v-model="form.topDay" :disabled="isTopDayDisabled()" @input="handleTopDayInput" placeholder="请输入1-7天有效范围内的置顶天数" />
            </el-form-item>
            </el-col>
            <el-col :span="20">
            <el-form-item label="内容">
                <editor 
                    v-model="form.noticeContent" 
                    :min-height="192" 
                    @update:model-value="handleEditorChange" 
                    :read-only = "readOnly" 
                    ref="editorRef"
                /> 
            </el-form-item>
            </el-col>
            <el-col :span="20">
            <el-form :model="form" :rules="rules" label-width="80px">
                <el-form-item label="上传附件">
                <fileUpload 
                    ref="fileUploadRef" 
                    @upload-success="handleUploadSuccess" 
                    @update:modelValue="handleFileChange"
                    @file-remove="handleFileRemove"
                    @immediate-submit="handleImmediateSubmit"
                    :initialFiles = "initialFiles"
                    :fileSecretOptions="filteredSysFileSecret"
                    :isEdit = "isEdit"
                    :disabled = "readOnly"
                    :multiple = "true"
                    :folderId = "0"
                />
                </el-form-item>
            </el-form>
            </el-col>
        </el-row>
        </el-form>
    </el-card>
    <!-- 提交组件 -->
    <submitVerify ref="submitVerifyRef" :task-variables="taskVariables" @submit-callback="submitCallback" />
    <!-- 审批记录 -->
    <approvalRecord ref="approvalRecordRef" />
</div>
<el-dialog v-model="previewDialogVisible" title="公告预览" width="100%" height="100%">
    <NoticeEditView :noticeInfo="form" :attachments="initialFiles" />
</el-dialog>
</div>
</template>
<script setup name="Notice" lang="ts">
import { getNotice, addNotice, updateNotice, deleteNoticeOssRelation } from '@/api/management/notice2';
import { NoticeForm, NoticeQuery, NoticeVO } from '@/api/management/notice2/types';
import { listByIds } from '@/api/system/oss';
import FileUpload from '@/components/FileUpload/index.vue';
import { startWorkFlow } from '@/api/workflow/task';
import SubmitVerify from '@/components/Process/submitVerify.vue';
import ApprovalRecord from '@/components/Process/approvalRecord.vue';
import ApprovalButton from '@/components/Process/approvalButton.vue';
import { AxiosResponse } from 'axios';
import { StartProcessBo } from '@/api/workflow/workflowCommon/types';
import { useRouter, useRoute } from 'vue-router';
import NoticeEditView from '@/views/management/notice2/noticeEditView.vue';

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_notice_type } = toRefs<any>(proxy?.useDict('sys_notice_status', 'sys_notice_type'));
const { sys_file_secret } = toRefs<any>(proxy?.useDict('sys_file_secret'));
const { sys_notice_istop } = toRefs<any>(proxy?.useDict('sys_notice_istop'));
// 过滤 sys_file_secret 字典数据
const filteredSysFileSecret = computed(() => {
    return sys_file_secret.value.filter((dict: any) => dict.value === '5' || dict.value === '10');
});
const fileUploadRef = ref<InstanceType<typeof FileUpload>>();

/* 审批组件相关定义 */
const buttonLoading = ref(false);
const loading = ref(true);
const routeParams = ref<Record<string, any>>({}); // 路由参数
const flowCode = ref<string>('');
const submitVerifyRef = ref<InstanceType<typeof SubmitVerify>>(); // 提交组件
const approvalRecordRef = ref<InstanceType<typeof ApprovalRecord>>(); // 审批记录组件
//按钮组件
const approvalButtonRef = ref<InstanceType<typeof ApprovalButton>>();
const noticeFormRef = ref<ElFormInstance>();
const submitFormData = ref<StartProcessBo>({
    businessId: '',
    flowCode: '',
    variables: {}
});
const taskVariables = ref<Record<string, any>>({});

const initFormData: NoticeForm = {
    noticeId: '',
    noticeTitle: '',
    noticeType: '',
    noticeContent: '',
    status: '0',
    remark: '',
    createByName: '',
    createByNickName: '',
    noticeSource: '',
    isTop: '0',
    topDay: undefined,
    downloadCount: 0,
    viewCount: 0,
    flowStatus: '',
    secret: '5',
    isRead: undefined,
    ossIds: []
};
// 获取是否置顶状态,赋值给topDay
const isTopDayDisabled = (): boolean => {
    if(form.value.isTop !== '1') {
        form.value.topDay = undefined;
        return true;
    }
    return false;
};

const data = reactive<PageData<NoticeForm, NoticeQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: '',
        createByName: '',
        createByNickName: '',
        status: '0',
        noticeType: undefined,
        secret: '',
        flowStatus: undefined
    },
    rules: {
        noticeTitle: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
        noticeType: [{ required: true, message: '公告类型不能为空', trigger: 'change' }],
        noticeContent: [{ required: true, message: "公告内容不能为空", trigger: "blur" }],
        secret: [{ required: true, message: '公告密级不能为空', trigger: 'change' }],
    }
});

const { form, rules } = toRefs(data);
const isEdit = ref(false); // 附件是否处于编辑状态
const initialFiles = ref([]); // 初始化附件列表
const readOnly = ref(false); // 编辑器是否只读

// 只能输入数字1-7的规则判定
const handleTopDayInput = (value: string) => {
    if (form.value.isTop === '1') {
        const parsedValue = parseInt(value);
        if (!isNaN(parsedValue) && parsedValue >= 1 && parsedValue <= 7) {
        form.value.topDay = parsedValue;
        } else {
        form.value.topDay = undefined;
        ElMessage.error('请输入1-7的数字');
        }
    }
};

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData };
    form.value.ossIds = []; // 清空 ossIds 列表
    isEdit.value = false; // 设置为新增状态
    initialFiles.value = []; // 清空初始文件列表
    noticeFormRef.value?.resetFields();
    form.value.noticeContent = '<p></p>';
};
/** 处理文件上传成功事件 */
const handleUploadSuccess = (fileInfo: { ossId: string; originalName: string; fileAll: any }) => {
    // 检查文件密级是否存在
    if (!fileInfo.fileAll.fileSecret) {
        ElMessage.error('请选择文件密级');
        return;
    }
    form.value.ossIds.push(fileInfo.ossId);
    // 上传多个文件时，必须使用push方法添加新文件，而不是直接赋值，否则文件会被覆盖
    initialFiles.value.push ({
        name: fileInfo.originalName,
        url: fileInfo.fileAll.url,
        ossId: fileInfo.ossId,
        uid: fileInfo.ossId,
        fileSecret: fileInfo.fileAll.fileSecret,
        status: 'success',
        progress: 100
    });

};
/** 处理文件变化事件 */
const handleFileChange = (newValue: string) => {
    // 如果文件被完全删除，清空相关字段
    if (!newValue) {
        initialFiles.value = [];
        // 确保ossIds也被清空，这样在提交时能正确传递文件已删除的信息
        form.value.ossIds = [];
    }
};

// 添加编辑器内容变化处理方法
const handleEditorChange = (content: string) => {
    form.value.noticeContent = content || '<p></p>';
};

/** 获取详情 */
const getInfo = () => {
    loading.value = true; // 开始加载数据
    buttonLoading.value = false;
    nextTick(async () => {
        try {
            const Id = routeParams.value.noticeId || routeParams.value.id;
            const res = await getNotice(Id);
            readOnly.value = routeParams.value.type === 'view' || routeParams.value.type === 'approval';
            
            Object.assign(form.value, res.data);
            // 确保 noticeContent 是一个有效的 HTML 字符串
            form.value.noticeContent = res.data.noticeContent || '<p></p>';

            // 初始化附件列表
            if (form.value.ossIds && form.value.ossIds.length > 0) {
                const res = await listByIds(form.value.ossIds.join(','));
                isEdit.value = true;
                initialFiles.value = res.data.map((file: any) => ({
                    name: file.originalName,
                    url: file.url,
                    ossId: file.ossId,
                    fileSecret: file.fileSecret,
                    uid: new Date().getTime() + initialFiles.value.length + 1,
                    isTemp: false, // 标记为非临时文件
                    status: 'success', // 标记为上传成功状态
                    progress: 100 // 标记为上传完成
                }));
            } else {
                form.value.ossIds = [];
                initialFiles.value = []; // 确保初始文件列表也被清空
            }
        } catch (error) {
            ElMessage.error('获取公告详情失败，请稍后再试');
            console.error(error);
        } finally {
            loading.value = false; // 数据加载完成
            buttonLoading.value = false;
        }
    });
};

/** 处理文件删除事件 */
const handleFileRemove = async (removedFile: any) => {
    // 从 initialFiles 中移除对应文件
    const index = initialFiles.value.findIndex(file => file.ossId === removedFile.ossId);
    if (index > -1) {
        initialFiles.value.splice(index, 1);
    }
    
    // 从 ossIds 中移除
    if (removedFile.ossId) {
        const ossIndex = form.value.ossIds.indexOf(removedFile.ossId);
        if (ossIndex > -1) {
        form.value.ossIds.splice(ossIndex, 1);
        }
        // 如果是编辑模式且有noticeId，调用删除关联关系的API
        if (isEdit.value && form.value.noticeId && removedFile.action === 'delete') {
            await deleteNoticeOssRelation(form.value.noticeId, [removedFile.ossId]);
        }
    }
};

/** 处理立即提交事件 */
const handleImmediateSubmit = async () => {
    // 如果是编辑模式且有noticeId，立即提交到后端
    if (isEdit.value && form.value.noticeId) {
        await updateNotice(form.value);
    }
};

/** 提交按钮 */
const submitForm = (flowStatus: string) => {
    try {   
        noticeFormRef.value?.validate(async (valid: boolean) => {
            if (valid) {
                buttonLoading.value = true;
                // 保存原始的密级字段数据
                const originalSecret = form.value.secret;
                // 通知公告和附件的密级比较
                let maxFileSecret = '0';
                if (form.value.ossIds && form.value.ossIds.length > 0) {
                    const res1 = await listByIds(form.value.ossIds.join(','));
                    if (res1.data.length > 0) {
                        maxFileSecret = res1.data.reduce((max, file) => { // 比较每个附件的密级获取最高密级
                            const maxNum = parseInt(max, 10);
                            const fileSecretNum = parseInt(file.fileSecret, 10);
                            return maxNum > fileSecretNum ? max : file.fileSecret;
                        }, '0');
                    }
                }
                // 将 secret 和 maxFileSecret 转换为数值类型进行比较
                // param radix=10 表示十进制
                const noticeSecretNum = parseInt(form.value.secret, 10);
                const maxFileSecretNum = parseInt(maxFileSecret, 10);
            
                if (noticeSecretNum >= maxFileSecretNum) {
                    let res: AxiosResponse<NoticeVO>;                  
                    if (form.value.noticeId) {
                        res = await updateNotice(form.value).finally(() => (buttonLoading.value = false));
                    } else {
                        res = await addNotice(form.value).finally(() => (buttonLoading.value = false));
                    }
                    // 保留原始密级，只更新后端存储加密字段
                    form.value = { 
                        ...res.data,
                        secret: originalSecret, // 保留用户看到的明文
                    }

                    if (flowStatus === 'draft') {
                        buttonLoading.value = false;
                        proxy?.$modal.msgSuccess('暂存成功');
                        proxy.$tab.closePage(route);
                        router.go(-1);
                    } else {
                        flowCode.value = 'notice';
                        await handleStartWorkFlow(res.data); // 启动工作流
                    }
                } else {
                    ElMessage.error('通知公告密级必须不低于附件密级');
                    buttonLoading.value = false;
                    return;
                }
            }
        });
    } catch (error) {
        ElMessage.error('提交失败，请稍后再试');
        console.error(error);
    }
    finally {
        buttonLoading.value = false;
    }
};
//提交申请
const handleStartWorkFlow = async (data: NoticeForm) => {
    try {
        if (!data || !data.noticeId) {
            throw new Error('Invalid data or missing noticeId');
        }

        submitFormData.value.flowCode = flowCode.value;
        submitFormData.value.businessId = data.noticeId;
        // 工作流启动时传递的流程变量
        taskVariables.value = {
            // userList: ['1', '3', '4']
        };
        submitFormData.value.variables = taskVariables.value;
        const resp = await startWorkFlow(submitFormData.value);
        if (submitVerifyRef.value) {
            buttonLoading.value = false;
            submitVerifyRef.value.openDialog(resp.data.taskId);
        }
    }
    finally {
        buttonLoading.value = false;
    }
};

//审批记录
const handleApprovalRecord = () => {
    approvalRecordRef.value.init(form.value.noticeId);
};
//提交回调
const submitCallback = async () => {
    await proxy.$tab.closePage(route);
    router.go(-1);
};
//审批
const approvalVerifyOpen = async () => {
    submitVerifyRef.value.openDialog(routeParams.value.taskId);
};

/** 添加发布前预览功能 */
// watch(
//     initialFiles,
//     (newFiles) => {
//         console.log('initialFiles updated:', newFiles);
//     },
//     { deep: true }
// );
// // 预览对话框显示状态
const previewDialogVisible = ref(false);
const previewPreLaunch = () => {
    previewDialogVisible.value = true;
}

onMounted(() => {
    nextTick(async () => {
        routeParams.value = route.query;
        reset();
        loading.value = false;
        if (routeParams.value.type === 'update' || routeParams.value.type === 'view' || routeParams.value.type === 'approval') {
            getInfo();
        }
    });
});
</script>
<style scoped lang="scss"></style>

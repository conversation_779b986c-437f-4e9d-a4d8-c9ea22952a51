import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CssrcSoftwareInfoVO, CssrcSoftwareInfoForm, CssrcSoftwareInfoQuery } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareInfo/types';

/**
 * 查询软件基本信息列表
 * @param query
 * @returns {*}
 */

export const listCssrcSoftwareInfo = (query?: CssrcSoftwareInfoQuery): AxiosPromise<CssrcSoftwareInfoVO[]> => {
  return request({
    url: '/cssrc/cssrcSoftwareInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询软件基本信息详细
 * @param softwareId
 */
export const getCssrcSoftwareInfo = (softwareId: string | number): AxiosPromise<CssrcSoftwareInfoVO> => {
  return request({
    url: '/cssrc/cssrcSoftwareInfo/' + softwareId,
    method: 'get'
  });
};

/**
 * 新增软件基本信息
 * @param data
 */
export const addCssrcSoftwareInfo = (data: CssrcSoftwareInfoForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改软件基本信息
 * @param data
 */
export const updateCssrcSoftwareInfo = (data: CssrcSoftwareInfoForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除软件基本信息
 * @param softwareId
 */
export const delCssrcSoftwareInfo = (softwareId: string | number | Array<string | number>) => {
  return request({
    url: '/cssrc/cssrcSoftwareInfo/' + softwareId,
    method: 'delete'
  });
};

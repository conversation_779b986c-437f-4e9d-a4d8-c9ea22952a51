import request from '@/utils/request';
import { OperLogQuery, OperLogVO, OperLogForm } from './types';
import { AxiosPromise } from 'axios';

// 查询操作日志列表
export function list(query: OperLogQuery): AxiosPromise<OperLogVO[]> {
  return request({
    url: '/monitor/operlog/list',
    method: 'get',
    params: query
  });
}

/**
 * 查询操作日志记录详细
 * @param operId
 */
export const getOperLog = (operId: string | number): AxiosPromise<OperLogVO> => {
  return request({
    url: '/system/operLog/' + operId,
    method: 'get'
  });
};

/**
 * 新增操作日志记录
 * @param data
 */
export const addOperLog = (data: OperLogForm) => {
  return request({
    url: '/system/operLog',
    method: 'post',
    data: data
  });
};

/**
 * 修改操作日志记录
 * @param data
 */
export const updateOperLog = (data: OperLogForm) => {
  return request({
    url: '/system/operLog',
    method: 'put',
    data: data
  });
};

// 删除操作日志
export function delOperlog(operId: string | number | Array<string | number>) {
  return request({
    url: '/monitor/operlog/' + operId,
    method: 'delete'
  });
}

// 清空操作日志
export function cleanOperlog() {
  return request({
    url: '/monitor/operlog/clean',
    method: 'delete'
  });
}

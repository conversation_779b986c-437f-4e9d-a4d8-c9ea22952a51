import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CssrcSoftwareCommentVO, CssrcSoftwareCommentForm, CssrcSoftwareCommentQuery } from '@/api/cssrc/cssrcSoftware/cssrcSoftwareComment/types';

/**
 * 查询软件评论列表
 * @param query
 * @returns {*}
 */

export const listCssrcSoftwareComment = (query?: CssrcSoftwareCommentQuery): AxiosPromise<CssrcSoftwareCommentVO[]> => {
  return request({
    url: '/cssrc/cssrcSoftwareComment/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询软件评论详细
 * @param commentId
 */
export const getCssrcSoftwareComment = (commentId: string | number): AxiosPromise<CssrcSoftwareCommentVO> => {
  return request({
    url: '/cssrc/cssrcSoftwareComment/' + commentId,
    method: 'get'
  });
};

/**
 * 新增软件评论
 * @param data
 */
export const addCssrcSoftwareComment = (data: CssrcSoftwareCommentForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareComment',
    method: 'post',
    data: data
  });
};

/**
 * 修改软件评论
 * @param data
 */
export const updateCssrcSoftwareComment = (data: CssrcSoftwareCommentForm) => {
  return request({
    url: '/cssrc/cssrcSoftwareComment',
    method: 'put',
    data: data
  });
};

/**
 * 删除软件评论
 * @param commentId
 */
export const delCssrcSoftwareComment = (commentId: string | number | Array<string | number>) => {
  return request({
    url: '/cssrc/cssrcSoftwareComment/' + commentId,
    method: 'delete'
  });
};
